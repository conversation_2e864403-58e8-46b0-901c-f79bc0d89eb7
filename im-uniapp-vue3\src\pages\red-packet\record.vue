<script setup>
import { ref, computed, getCurrentInstance } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';

import {
  reqOrderRedEnvelopeReceiveRecordList,
  reqOrderRedEnvelopeSendRecordList
} from '@/apis/red-packet';
import { getFormatMoney } from '@/common';

const THIS = getCurrentInstance().proxy;

// 记录类型字典
const typeMap = {
  1: {
    title: '收到的红包',
    totalText: '共收到',
    reqMethod: reqOrderRedEnvelopeReceiveRecordList,
    filedList: 'receiveList',
    fielGoDetailOrderNo: 'linkOrderNo'
  },
  2: {
    title: '发出的红包',
    totalText: '共发出',
    reqMethod: reqOrderRedEnvelopeSendRecordList,
    filedList: 'orderRedEnvelopeSends',
    fielGoDetailOrderNo: 'orderNo'
  }
};

// 记录类型列表
const typeList = Object.keys(typeMap).map((key) => {
  return {
    id: Number(key),
    name: typeMap[key].title
  };
});

// 当前选择的记录类型
const refCurType = ref(1);
// 选择红包类型弹层
const refPopupType = ref(null);
// 详情信息
const refDetailInfo = ref({});

// 当前选择的记录类型对象
const cpdCurType = computed(() => typeMap[refCurType.value]);

// 打开类型弹层
function doChooseRedPacketType() {
  refPopupType.value.open();
}

// 选择类型回调
function onSelectMenuItem(item) {
  refCurType.value = item.id;
  getData();
  refPopupType.value.close();
}

// 取消类型回调
function onCancelMenuItem() {
  refPopupType.value.close();
}

// 获取数据
function getData() {
  cpdCurType.value.reqMethod().then((res) => {
    refDetailInfo.value = res;
  });
}

// 去详情页
function goDetail(detail) {
  uni.navigateTo({
    url: '/pages/red-packet/detail?id=' + detail[cpdCurType.value.fielGoDetailOrderNo]
  });
}

onLoad((options) => {
  refCurType.value = options.type || 1;
  getData();
});

onShow(() => {});
</script>

<template>
  <page-wrapper>
    <view class="page m-page-red-packet-record">
      <nav-bar back more @more="doChooseRedPacketType" isColorWhite>
        {{ cpdCurType.title }}
      </nav-bar>

      <view class="u-content">
        <!-- 头像 -->
        <head-image
          class="u-avatar"
          :id="THIS.userStore.userInfo.id"
          :url="THIS.userStore.userInfo.headImage"
          :name="THIS.userStore.userInfo.nickName"
          :size="120"
        ></head-image>

        <!-- 名称 -->
        <view class="u-name">
          {{ THIS.userStore.userInfo.nickName }}{{ cpdCurType.totalText }}
        </view>

        <!-- 金额 -->
        <view class="u-amount">
          <view class="u-value"> {{ getFormatMoney(refDetailInfo.totalAmount) }} </view>元
        </view>

        <!-- 数据 - 收到的红包 -->
        <view class="u-datas" v-if="refCurType === 1">
          <view class="u-data-item">
            <view class="u-item-value"> {{ getFormatMoney(refDetailInfo.totalNum) }} </view>
            <view class="u-item-name"> 收到红包 </view>
          </view>
          <view class="u-data-item">
            <view class="u-item-value"> {{ refDetailInfo.totalTopOne }} </view>
            <view class="u-item-name"> 手气最佳 </view>
          </view>
        </view>
        <!-- 数据 - 发出的红包 -->
        <view class="u-data-info" v-else>
          发出的红包总数
          <text class="u-value">{{ refDetailInfo.totalNum }} </text>个
        </view>

        <!-- 列表 -->
        <view class="u-list">
          <view
            class="u-list-item"
            v-for="(item, index) in refDetailInfo[cpdCurType.filedList]"
            :key="index"
            @tap="goDetail(item)"
          >
            <view class="u-item-info">
              <view class="u-info-name">
                {{ item.nickName }}
              </view>
              <view class="u-info-time">{{ item.createTime }}</view>
            </view>
            <view class="u-item-amount">{{ getFormatMoney(item.amount) }}元</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选择类型弹层 -->
    <popup-menu
      ref="refPopupType"
      :items="typeList"
      @select="onSelectMenuItem"
      @cancel="onCancelMenuItem"
    ></popup-menu>
  </page-wrapper>
</template>

<style lang="scss" scoped>
.m-page-red-packet-record {
  .im-nav-bar {
    --background-color: #de3a39;
  }

  .u-content {
    padding: 66rpx 0;
    .u-avatar {
      display: flex;
      justify-content: center;
    }
    .u-name {
      font-size: 32rpx;
      margin-top: 20rpx;
      text-align: center;
    }
    .u-amount {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      font-family: var(--font-family-inter);
      font-size: 32rpx;
      line-height: 42rpx;
      margin-top: 16rpx;
      .u-value {
        font-size: 64rpx;
        line-height: 72rpx;
      }
    }
    .u-datas {
      display: flex;
      margin-top: 36rpx;
      .u-data-item {
        flex: 1;
        text-align: center;
        color: rgba(0, 0, 0, 0.4);
        .u-item-value {
          font-size: 32rpx;
          line-height: 42rpx;
          height: 42rpx;
        }
        .u-item-name {
          font-size: 24rpx;
          line-height: 34rpx;
        }
      }
    }
    .u-data-info {
      margin-top: 36rpx;
      padding-bottom: 40rpx;
      text-align: center;
      font-size: 32rpx;
      color: var(--text-color-weak-1);
      .u-value{
        color: #D0A65D;
      }
    }

    .u-list {
      margin-top: 40rpx;
      border-top: 2rpx solid var(--border-normal);
      .u-list-item {
        position: relative;
        display: flex;
        align-items: center;
        column-gap: 22rpx;
        padding: 18rpx 28rpx;
        background: var(--bg-card);
        border-bottom: 2rpx solid var(--border-normal);
        .u-item-info {
          flex: 1;
          .u-info-name {
            font-size: 32rpx;
          }
          .u-info-time {
            font-size: 24rpx;
            color: var(--text-color-weak-1);
            margin-top: 4px;
          }
        }
        .u-item-amount {
          align-self: flex-start;
          margin-left: auto;
          font-size: 32rpx;
        }
      }
    }
  }
}
</style>
