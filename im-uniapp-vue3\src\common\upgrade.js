import http from './request';
import dialog from './dialog';
import CONFIG from '@/configs';

let checkAndUpgrade = () => {
	plus.runtime.getProperty(plus.runtime.appid, (info) => {
		const curVersion = info.version;
		console.log("当前应用版本：", curVersion);
		http({
			url: '/system/checkVersion?version=' + curVersion
		}).then((res) => {
			if (!res.isLatestVersion) {
				dialog.open({
					title: "发现新版本",
					confirmText: "立即更新",
					cancelText: "稍后再说",
					content: res.changeLog,
					icon: '/static/image/upgrade.png',
					success: () => {
						downloadAndInstall();
					}
				})
			}
		}).catch(() => {
			console.log("获取版本信息异常");
		})
	});
}


let downloadAndInstall = () => {
	const wgtUrl = CONFIG.WGT_URL;
	console.log('开始下载更新包，URL:', wgtUrl);

	const dtask = plus.downloader.createDownload(wgtUrl, {}, (d, status) => {
		console.log('下载完成，状态:', status, '文件路径:', d.filename);

		if (status == 200) {
			// 检查文件大小
			plus.io.getFileInfo({
				filePath: d.filename,
				success: function (info) {
					console.log('下载文件信息:', JSON.stringify(info));
					console.log('文件大小:', info.size, '字节');

					if (info.size > 1000) { // 确保文件大小合理
						install(d.filename); // 安装wgt包
					} else {
						const errorMsg = '下载的更新包大小异常: ' + info.size + ' 字节';
						console.error(errorMsg);
						plus.nativeUI.alert(errorMsg);
					}
				},
				fail: function (e) {
					console.error('获取文件信息失败:', JSON.stringify(e));
					console.log('尝试直接安装下载的文件');
					install(d.filename); // 仍然尝试安装
				}
			});
		} else {
			const errorMsg = '下载失败，状态码: ' + status;
			console.error(errorMsg);
			plus.nativeUI.alert(errorMsg);
		}
	});

	downloadProgress(dtask);
}


let install = (path) => {
	let showLoading = plus.nativeUI.showWaiting('正在安装...');
	console.log('开始安装wgt包，路径:', path);

	plus.runtime.install(path, {}, () => {
		console.log('安装成功，准备重启应用');
		showLoading.setTitle('安装成功,应用即将重启...');
		// 重启应用
		setTimeout(() => {
			plus.nativeUI.closeWaiting();
			plus.runtime.restart()
		}, 1500)
	}, (e) => {
		// 详细记录错误信息
		console.error('安装失败详细信息:', JSON.stringify(e));

		// 显示详细错误信息
		let errorMsg = '更新失败';
		if (e) {
			if (typeof e === 'string') {
				errorMsg += ': ' + e;
			} else if (e.message) {
				errorMsg += ': ' + e.message;
			} else if (e.code) {
				errorMsg += ': 错误代码 ' + e.code;
			} else {
				errorMsg += ': ' + JSON.stringify(e);
			}
		}

		// 记录到控制台
		console.error(errorMsg);

		// 显示给用户
		plus.nativeUI.alert(errorMsg);
	});
}

let downloadProgress = (dtask) => {
	try {
		console.log('启动下载任务');
		dtask.start(); //开启下载任务
		let showLoading = plus.nativeUI.showWaiting('正在下载');
		let timeStamp = new Date().getTime();

		dtask.addEventListener('statechanged', (task) => {
			// 给下载任务设置监听
			console.log('下载状态变化:', task.state,
				'已下载:', task.downloadedSize,
				'总大小:', task.totalSize);

			switch (task.state) {
				case 1:
					console.log('下载任务开始');
					break;
				case 2:
					console.log('下载任务已连接到服务器');
					break;
				case 3:
					let prg = parseInt((parseFloat(task.downloadedSize) / parseFloat(task.totalSize)) * 100);
					let curTime = new Date().getTime();
					if (curTime > timeStamp + 100) {
						console.log('下载进度:', prg + '%');
						showLoading.setTitle('正在下载' + prg + '%');
						timeStamp = curTime
					}
					break;
				case 4:
					// 下载完成
					console.log('下载任务已完成');
					plus.nativeUI.closeWaiting();
					break;
				default:
					console.log('未知下载状态:', task.state);
					break;
			}
		})
	} catch (e) {
		console.error('下载过程出错:', JSON.stringify(e));
		plus.nativeUI.closeWaiting();

		let errorMsg = '下载更新失败';
		if (e) {
			if (typeof e === 'string') {
				errorMsg += ': ' + e;
			} else if (e.message) {
				errorMsg += ': ' + e.message;
			} else {
				errorMsg += ': ' + JSON.stringify(e);
			}
		}

		console.error(errorMsg);
		uni.showToast({
			title: errorMsg,
			icon: 'none',
			duration: 3000
		})
	}
}

export {
	checkAndUpgrade
}