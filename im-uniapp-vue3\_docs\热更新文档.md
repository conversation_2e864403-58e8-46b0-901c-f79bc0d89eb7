# 热更新说明
首先 先吧这个版本号需要告诉后台 多少 更新了什么东西
其次 需要打一下wgt包 app运行发布 里面有一个wgt包 打完包吧包重名名为im.wgt
然后 这个wgt 需要上传到 index.js 里面的 WGT_URL 里面 这个需要打包放上去 你看看找运维还是说直接打到H5 上面 H5 上面没法访问

npm run dev:prod:ycl 
目前没法自动 只需要改版本应用名称 不需要改应用版本号
然后再执行 huildx 的打wgt包 再吧wgt包放到
aaPanel Internet Address: https://************:33185/eb1522f8
aaPanel Internal Address: https://***********:33185/eb1522f8
username: djdq8yp0
password: d5178a4d

/www/wwwroot/privacy.jrtxa.com/yunchangliao/prod
改下名 改成im.wgt