<script setup>
import { ref } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';

import { reqRedEnvelopeCoverList } from '@/apis/red-packet';

// 红包封面列表
const refRedPacketCoverList = ref([]);

function getData(){
  reqRedEnvelopeCoverList().then((res) => {
    refRedPacketCoverList.value = res.records;
  });
}

function doChooseRedPacketType(item){
  uni.$emit('chooseRedPacketType', item);
  uni.navigateBack();
}

onLoad((options) => {
  getData();
});

onShow(() => {
  
});
</script>

<template>
  <page-wrapper>
    <view class="page m-page-red-packet-covers">
      <nav-bar back>红包封面</nav-bar>

      <view class="u-main">
        <view class="u-list">
          <view class="u-list-item" v-for="(item, index) in refRedPacketCoverList" :key="index">
            <image :src="item.coverAddr" mode="aspectFill" class="u-cover"></image>
            <view class="u-item-name">
              {{ item.coverName }}
            </view>
            <up-button
              class="u-btn"
              type="primary"
              size="mini"
              @tap="doChooseRedPacketType(item)"
              >使用</up-button>
          </view>
        </view>
      </view>
    </view>
  </page-wrapper>
</template>

<style lang="scss" scoped>
.m-page-red-packet-covers{
  .u-main{
    padding: 24rpx;
  }
  .u-list{
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    row-gap: 24rpx;
    .u-list-item{
      background: var(--bg-card-deeper);
      width: 346rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 10rpx;
      padding: 18rpx 0;
      .u-cover{
        width: 264rpx;
        height: 438rpx;
      }
      .u-item-name{
        font-size: 28rpx;
        line-height: 34rpx;
        margin-top: 6rpx;
      }
      .u-btn{
        margin-top: 14rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10rpx;
        width: 144rpx;
        height: 56rpx;
        background: #DE3A39;
        color: #fff;
        font-size: 32rpx;
        border: none;
      }
    }
  }
}
</style>
