#!/bin/bash

echo "==================================================="
echo "构建H5应用并复制wgt包到H5根目录"
echo "==================================================="

# 切换到项目根目录
cd ../../../

echo "1. 检查wgt包是否存在..."
node -e "const fs=require('fs');const path=require('path');const dirs=['unpackage/release','unpackage/wgt'];let found=false;for(const dir of dirs){const p=path.resolve(__dirname,dir);if(fs.existsSync(p)){const files=fs.readdirSync(p).filter(f=>f.endsWith('.wgt'));if(files.length>0){found=true;break;}}}if(!found){console.error('错误: 未找到wgt包文件。请先通过HBuilderX生成wgt包。');process.exit(1);}"

if [ $? -ne 0 ]; then
  echo "请先通过HBuilderX生成wgt包，然后再运行此脚本。"
  echo "在HBuilderX中: 发行 -> 原生App-制作应用wgt包"
  read -p "按回车键继续..."
  exit 1
fi

echo "2. 选择构建环境:"
echo "  1) 测试环境 (test)"
echo "  2) 生产环境 (prod)"
echo "  3) 云畅聊测试环境 (ycl-test)"
echo "  4) 云畅聊生产环境 (ycl-prod)"

read -p "请选择构建环境 (1-4): " env_choice

case $env_choice in
  1)
    build_cmd="npm run build:test"
    ;;
  2)
    build_cmd="npm run build:prod"
    ;;
  3)
    build_cmd="npm run build:test:ycl"
    ;;
  4)
    build_cmd="npm run build:prod:ycl"
    ;;
  *)
    echo "无效的选择!"
    read -p "按回车键继续..."
    exit 1
    ;;
esac

echo "3. 开始构建H5应用..."
eval $build_cmd

if [ $? -ne 0 ]; then
  echo "构建失败!"
  read -p "按回车键继续..."
  exit 1
fi

echo "4. 构建完成，wgt包已复制到H5根目录。"
echo "您可以在dist/build/h5或dist/build/ycl/h5目录中找到app.wgt文件。"

read -p "按回车键继续..."
