# HBuilderx云打包cli说明

相关文档：https://hx.dcloud.net.cn/cli/README

加入到环境变量（控制面板 - 系统 - 高级系统设置 - 环境变量，在path里把hbuild根目录加进去）

此时可以输入
```
cli help
```
来成功执行

## 相关命令

1. 打包前记得升版本号
2. 不要单独修改manifest.json，而是修改manifest.{项目名}.json文件，因为打包时会用这个文件覆盖manifest.json
3. 打包以后本地修改生成的文件每次打包都会重新替换，所以可以放心传git

### 创信密聊
- yarn build:app-cloud:test
  打安卓、iOS测试包
- yarn build:app-cloud-android:test
  单独打安卓测试包
- yarn build:app-cloud-ios:test
  单独打iOS测试包
- yarn build:app-cloud:prod
  打安卓、iOS正式包
- yarn build:app-cloud-android:prod
  单独打安卓正式包
- yarn build:app-cloud-ios:prod
  单独打iOS正式包

### 云畅聊
- yarn build:app-cloud:test:ycl
  打安卓、iOS测试包
- yarn build:app-cloud-android:test:ycl
  单独打安卓测试包
- yarn build:app-cloud-ios:test:ycl
  单独打iOS测试包
- yarn build:app-cloud:prod:ycl
  打安卓、iOS正式包
- yarn build:app-cloud-android:prod:ycl
  单独打安卓正式包
- yarn build:app-cloud-ios:prod:ycl
  单独打iOS正式包