// 选择群成员类型字典
export const mapChooseMemberType = {
  // 专属红包选择领取人
  1: {
    type: 1,
    name: '选择领取人',
    // 是否多选模式
    isSelect: false,
    // 不禁用任何成员
    isSelectJudgeDisable: function (member) {
      return false;
    }
  },
  // 转让群主时选择
  2: {
    type: 2,
    name: '选择新群主',
    // 是否多选模式
    isSelect: false,
    // 不禁用任何成员
    isSelectJudgeDisable: function (member) {
      return false;
    }
  },
  // 选择管理员
  3: {
    type: 3,
    name: '选择新成员',
    // 是否多选模式
    isSelect: true,
    // 将已经是管理员的成员禁用
    isSelectJudgeDisable: function (member) {
      return member.groupMemberType === 1;
    }
  }
};
