<script setup>
import { ref } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';

import { reqOrderRedEnvelopeReceiveRecord } from '@/apis/red-packet';
import { getFormatMoney } from '@/common';

// 记录类型字典
const typeMap = {
  1: {
    title: '收到的红包'
  },
  2: {
    title: '发出的红包'
  }
};

// 记录类型列表
const typeList = Object.keys(typeMap).map((key) => {
  return {
    id: Number(key),
    name: typeMap[key].title
  };
});

const refId = ref('');
// 等待状态
const refLoading = ref(true);
// 选择红包类型弹层
const refPopupType = ref(null);
// 详情信息
const refDetailInfo = ref({});

// 打开类型弹层
function doChooseRedPacketType() {
  refPopupType.value.open();
}

// 去钱包页
function goWallet(){
  uni.navigateTo({
    url: '/pages/common/myWallet'
  });
}

// 选择类型回调
function onSelectMenuItem(item) {
  // 跳转到红包记录列表
  uni.navigateTo({
    url: '/pages/red-packet/record?type=' + item.id
  });
  refPopupType.value.close();
}

// 取消类型回调
function onCancelMenuItem() {
  refPopupType.value.close();
}

function getData() {
  refLoading.value = true;
  uni.showLoading();
  reqOrderRedEnvelopeReceiveRecord(refId.value)
    .then((res) => {
      refDetailInfo.value = res;
    })
    .finally(() => {
      refLoading.value = false;
      uni.hideLoading();
    });
}

onLoad((options) => {
  refId.value = options.id;
  getData();
});

onShow(() => {});
</script>

<template>
  <page-wrapper v-if="!refLoading">
    <view class="page m-page-red-packet-detail">
      <nav-bar
        back
        more
        @more="doChooseRedPacketType"
        isBgTransparent
        isColorWhite
        :hasBottomBorder="false"
      ></nav-bar>
      <view class="u-red-packet-bg-wrap" :class="{ 's-has-bg': refDetailInfo.redCover }">
        <!-- 背景 -->
        <view
          v-if="refDetailInfo.redCover"
          class="u-bg"
          :style="{
            background: `#f35541 url(${refDetailInfo.redCover}) no-repeat center -60rpx / cover`
          }"
        ></view>
      </view>
      <!-- 内容 -->
      <view class="u-content">
        <!-- 发送人信息 -->
        <view class="u-name">
          <head-image
            class="u-avatar"
            :id="refDetailInfo.sendUserId"
            :url="refDetailInfo.sendHeadImage"
            :name="refDetailInfo.sendNickName"
            :size="42"
          ></head-image>
          {{ refDetailInfo.sendNickName }}发出的红包
          <!-- 拼手气红包标识 -->
          <image v-if="refDetailInfo.type === 1" src="@/themes/common/images/red-packet/pin-plain.png" mode="widthFix" class="u-img" />
        </view>

        <!-- 红包备注 -->
        <view class="u-remark">
          {{ refDetailInfo.remark }}
        </view>

        <!-- 我领取的金额 -->
        <view class="u-my-amount" v-if="refDetailInfo.myAmount > 0">
          <view class="u-amount-info">
            <text class="u-value">
              {{ getFormatMoney(refDetailInfo.myAmount) }}
            </text>元
          </view>
          <view class="u-tips" @tap="goWallet">
            已存入零钱，可直接消费 >
          </view>
        </view>

        <!-- 金额情况 -->
        <view class="u-amount">
          <!-- 已领完0|已结束2 -->
          <block v-if="refDetailInfo.status === 0 || refDetailInfo.status === 2">
            {{ refDetailInfo.num }}个红包共{{ getFormatMoney(refDetailInfo.amount) }}元
          </block>
          <!-- 待领取 -->
          <block v-else>
            <!-- 私聊，或者群聊的专属红包 -->
            <block v-if="refDetailInfo.targetType === 2 || refDetailInfo.type === 3">
              红包金额{{ getFormatMoney(refDetailInfo.amount) }}元，等待对方领取
            </block>
            <!-- 群聊 -->
            <block v-else>
              已领取{{ getFormatMoney(refDetailInfo.num - refDetailInfo.lastNum) }}/{{
                refDetailInfo.num
              }}个红包，共{{ getFormatMoney(refDetailInfo.amount - refDetailInfo.lastAmount) }}/{{
                getFormatMoney(refDetailInfo.amount)
              }}元
            </block>
          </block>
        </view>

        <!-- 领取列表 -->
        <view class="u-list">
          <view
            class="u-list-item"
            v-for="(item, index) in refDetailInfo.redEnvelopeReceives"
            :key="index"
          >
            <head-image
              class="u-item-avatar"
              :id="item.userId"
              :url="item.headImage"
              :name="item.nickName"
              :size="92"
            ></head-image>
            <view class="u-item-info">
              <view class="u-info-name">{{ item.nickName }}</view>
              <view class="u-info-time">{{ item.createTime }}</view>
            </view>
            <view class="u-item-right">
              <view class="u-item-amount">
                {{ getFormatMoney(item.amount) }}元
              </view>
              <!-- 必须是拼手气红包，才能显示手气最佳 -->
              <view class="u-item-status" v-if="refDetailInfo.type === 1 && item.topOne === 1">
                <image src="@/themes/common/images/red-packet/best.png" mode="widthFix" class="u-img" />
                手气最佳
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选择类型弹层 -->
    <popup-menu
      ref="refPopupType"
      :items="typeList"
      @select="onSelectMenuItem"
      @cancel="onCancelMenuItem"
    ></popup-menu>
  </page-wrapper>
</template>

<style lang="scss" scoped>
.m-page-red-packet-detail {
  --page-top: 1px;
  --bg-margin-top: calc(-100rpx - var(--tab-bar-height));

  .u-red-packet-bg-wrap {
    position: relative;
    transform: translateY(var(--bg-margin-top));
    padding: 0 64rpx;
    width: 100%;
    height: 312rpx;
    background: #de3a39;
    border-radius: 100%;
    margin-left: -32rpx;
    &.s-has-bg{
      height: 624rpx;
      overflow: hidden;
      padding: 0 200rpx;
      margin-left: -200rpx;
      .u-bg{
        height: 624rpx;
      }
    }
  }

  .u-content {
    margin-top: calc(56rpx + var(--bg-margin-top));
    .u-name {
      display: flex;
      align-items: center;
      justify-content: center;
      column-gap: 18rpx;
      font-size: 32rpx;
      .u-img{
        width: 32rpx;
        height: auto;
      }
    }
    .u-remark {
      text-align: center;
      color: var(--text-color-weak-1);
      font-size: 24rpx;
      margin-top: 18rpx;
    }
    .u-amount {
      font-size: 24rpx;
      line-height: 34rpx;
      padding: 8rpx 28rpx;
      border-bottom: 2rpx solid var(--border-normal);
      color: var(--text-color-weak-1);
      margin-top: 84rpx;
    }

    .u-my-amount{
      margin-top: 44rpx;
      .u-amount-info{
        font-size: 32rpx;
        line-height: 52rpx;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        .u-value{
          font-size: 64rpx;
          line-height: 74rpx;
          margin-right: 10rpx;
          font-weight: bold;
        }
      }
      .u-tips{
        margin-top: 8rpx;
        font-size: 24rpx;
        line-height: 30rpx;
        text-align: center;
      }
    }

    .u-list {
      .u-list-item {
        position: relative;
        display: flex;
        align-items: center;
        column-gap: 22rpx;
        padding: 18rpx 28rpx;
        &:after {
          content: '';
          position: absolute;
          left: calc(28rpx + 92rpx + 22rpx);
          right: 0;
          bottom: 0;
          height: 2rpx;
          background: var(--border-normal);
        }
        .u-item-avatar {
          flex-shrink: 0;
        }
        .u-item-info {
          flex: 1;
          .u-info-name {
            font-size: 32rpx;
          }
          .u-info-time {
            font-size: 24rpx;
            color: var(--text-color-weak-1);
            margin-top: 4px;
          }
        }
        .u-item-right{
          align-self: flex-start;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          margin-left: auto;
        }
        .u-item-amount {
          font-size: 32rpx;
        }
        .u-item-status{
          display: flex;
          align-items: center;
          font-size: 24rpx;
          color: #fbb335;
          margin-top: 8rpx;
          .u-img{
            width: 36rpx;
            height: auto;
            margin-right: 8rpx;
          }
        }
      }
    }
  }
}
</style>
