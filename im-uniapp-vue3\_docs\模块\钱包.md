# 钱包模块

## 1. 功能概述

钱包模块是应用中的资金管理系统，允许用户查看余额、充值、转账和查看交易记录。

## 2. 功能列表

### 2.1 我的钱包
- "我的"页面增加"我的钱包"入口，根据后端返回决定是否展示
- 显示用户当前余额
- 提供转账和交易记录入口
- 右上角文字根据 userInfo.isSetPassword 状态显示"设置支付密码"或"修改支付密码"

### 2.2 充值功能
- 点击充值弹框让用户联系客服充值

### 2.3 转账功能
- 允许用户向其他账户转账
- 转账前检查支付密码设置状态，若未设置则跳转到密码设置页面
- 转账前检查余额是否为0，若为0则提示"总资产不足，请充值后再试"
- 转账成功后返回钱包页面
- 划转账户页面可一次性划转全部 点击全部获取最新余额进行划转

### 2.4 交易记录
- 显示所有交易记录
- 支持自动分页加载，滚动到底部时自动加载更多记录
- 显示交易状态、金额和时间
- 交易状态：待审核(pending)、已通过(approved)、已拒绝(rejected)

### 2.5 支付密码管理
- 设置/修改支付密码
- 支付密码验证（6位数字）
- 修改密码手机号不可编辑 获取手机号验证码后进行修改

## 3. 业务规则

### 3.1 支付密码
- 首次使用钱包功能需设置支付密码
- 支付密码为6位数字
- 重要操作（如转账）需验证支付密码

### 3.2 交易处理
- 转账操作需实时更新余额 故每次进入页面刷新记录和余额
- 交易记录按时间倒序排列

### 4.3 安全措施
- 密码输入使用掩码显示
