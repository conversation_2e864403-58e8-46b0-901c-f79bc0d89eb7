@echo off
echo ===================================================
echo 构建H5应用并复制wgt包到H5根目录
echo ===================================================

cd ..\..\..

echo 1. 检查wgt包是否存在...
node -e "const fs=require('fs');const path=require('path');const dirs=['unpackage/release','unpackage/wgt'];let found=false;for(const dir of dirs){const p=path.resolve(__dirname,dir);if(fs.existsSync(p)){const files=fs.readdirSync(p).filter(f=>f.endsWith('.wgt'));if(files.length>0){found=true;break;}}}if(!found){console.error('错误: 未找到wgt包文件。请先通过HBuilderX生成wgt包。');process.exit(1);}"

if %errorlevel% neq 0 (
  echo 请先通过HBuilderX生成wgt包，然后再运行此脚本。
  echo 在HBuilderX中: 发行 -^> 原生App-制作应用wgt包
  pause
  exit /b 1
)

echo 2. 选择构建环境:
echo   1) 测试环境 (test)
echo   2) 生产环境 (prod)
echo   3) 云畅聊测试环境 (ycl-test)
echo   4) 云畅聊生产环境 (ycl-prod)

set /p env_choice="请选择构建环境 (1-4): "

if "%env_choice%"=="1" (
  set build_cmd=npm run build:test
) else if "%env_choice%"=="2" (
  set build_cmd=npm run build:prod
) else if "%env_choice%"=="3" (
  set build_cmd=npm run build:test:ycl
) else if "%env_choice%"=="4" (
  set build_cmd=npm run build:prod:ycl
) else (
  echo 无效的选择!
  pause
  exit /b 1
)

echo 3. 开始构建H5应用...
call %build_cmd%

if %errorlevel% neq 0 (
  echo 构建失败!
  pause
  exit /b 1
)

echo 4. 构建完成，wgt包已复制到H5根目录。
echo 您可以在dist/build/h5或dist/build/ycl/h5目录中找到app.wgt文件。

pause
