<template>
	<page-wrapper>
		<view class="page set-password">
			<nav-bar back>设置支付密码</nav-bar>

			<view class="password-content">
				<!-- <view class="password-title">设置支付密码</view> -->
				<view class="password-subtitle">请设置支付密码，用于支付验证</view>

				<!-- 密码输入框 -->
				<view class="password-input-box">
					<up-code-input v-model="password" :maxlength="6" :focus="passwordFocus" dot mode="box" :size="40"
						@finish="onPasswordFinish"></up-code-input>
				</view>

				<!-- 确认密码输入框 (仅在第一次输入完成后显示) -->
				<view v-if="step === 2" class="confirm-section">
					<view class="password-subtitle">请再次输入密码确认</view>
					<view class="password-input-box">
						<up-code-input v-model="confirmPassword" :maxlength="6" :focus="confirmFocus" dot mode="box"
							:size="40" @finish="onConfirmFinish"></up-code-input>
					</view>
				</view>
			</view>
		</view>
	</page-wrapper>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import { setPayPassword } from "@/apis/mine/index";

const THIS = getCurrentInstance().proxy;

// 表单数据
const password = ref('');
const confirmPassword = ref('');
const passwordFocus = ref(false);
const confirmFocus = ref(false);
const step = ref(1); // 1: 输入密码, 2: 确认密码

// 页面加载时自动聚焦密码输入框
onMounted(() => {
	setTimeout(() => {
		passwordFocus.value = true;
	}, 300);
});

// 第一次密码输入完成
const onPasswordFinish = (value) => {
	// 验证密码是否为6位数字
	if (!/^\d{6}$/.test(value)) {
		uni.showToast({
			title: '请输入6位数字密码',
			icon: 'none'
		});
		password.value = '';
		setTimeout(() => {
			passwordFocus.value = true;
		}, 300);
		return;
	}

	// 进入确认密码步骤
	step.value = 2;
	setTimeout(() => {
		confirmFocus.value = true;
	}, 300);
};

// 确认密码输入完成
const onConfirmFinish = (value) => {
	// 验证两次密码是否一致
	if (password.value !== confirmPassword.value) {
		uni.showToast({
			title: '两次密码输入不一致',
			icon: 'none'
		});
		confirmPassword.value = '';
		setTimeout(() => {
			confirmFocus.value = true;
		}, 300);
		return;
	}

	// 提交密码
	submitPassword();
};

// 提交密码
const submitPassword = () => {
	// 显示加载中
	uni.showLoading({
		title: '提交中'
	});

	const params = {
		payPassword: password.value
	};

	// 调用设置密码接口
	setPayPassword(params).then(() => {
		uni.hideLoading();
		// 显示成功提示
		uni.showToast({
			title: '设置成功',
			icon: 'success'
		});

		// 更新用户信息
		THIS.userStore.loadUser();
		
		// 返回上一页
		setTimeout(() => {
			uni.navigateBack();
		}, 500);
	}).catch(err => {
		uni.hideLoading();
		uni.showToast({
			title: err.message || '密码设置失败',
			icon: 'none'
		});
	});
};
</script>

<style lang="scss" scoped>
.set-password {
	background-color: var(--bg);
	position: relative;

	.password-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 60vh;
	}

	.password-title {
		font-size: 18px;
		font-weight: 500;
		color: var(--text-color);
		margin-bottom: 20rpx;
		text-align: center;
	}

	.password-subtitle {
		font-size: 14px;
		color: var(--text-color-light);
		margin-bottom: 60rpx;
		text-align: center;
	}

	.password-input-box {
		width: 100%;
		margin: 0 auto 60rpx;
		display: flex;
		justify-content: center;

		:deep(.u-code-input) {
			width: 90%;
			justify-content: center;

			.u-code-input__item {
				border: 1px solid var(--border-color);
				border-radius: 0;
				margin: 0 8px;
				background-color: var(--bg-input);
			}
		}
	}

	.confirm-section {
		width: 100%;
		margin-top: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.close-btn {
		position: absolute;
		top: 30rpx;
		left: 30rpx;
		font-size: 24px;
		color: var(--text-color-light);
		width: 40rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
		z-index: 10;
	}
}
</style>
