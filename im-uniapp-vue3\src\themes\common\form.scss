// 表单样式
.m-form {
  font-size: 32rpx;

  .u-form-item {
    font-size: 32rpx !important;
    padding: 10rpx 0;
    color: var(--text-color) !important;

    // 左边图标
    .u-icon-left {
      width: 24rpx;
      margin-right: 20rpx;
      &.s-pin {
        width: 32rpx;
        margin-right: 16rpx;
      }
    }

    .u-textarea {
      background-color: transparent;
    }
    .u-textarea__field {
      font-size: inherit;
    }

    .u-input__content__field-wrapper__field, .uni-textarea-textarea{
      color: var(--text-color) !important;
    }

    .input-placeholder {
      color: var(--common-form-placeholder-color) !important;
    }

    .u-form-item__body {
      padding: 0;
      border: 2rpx solid var(--common-form-border-color);
      background: var(--bg-card);
      border-radius: 10rpx;
      padding: 0 28rpx;
      min-height: 104rpx;
      .u-form-item__body__left {
        width: auto !important;
        .u-form-item__body__left__content__label {
          font-size: inherit;
        }
      }
      .u-form-item__body__right {
        .u-form-item__body__right__content {
          height: 100%;
        }
      }
    }

    .u-form-item__body__right__message{
      text-align: right;
      padding: 20rpx 0;
    }
  }

  // 提示
  .u-form-item-tips {
    color: var(--common-form-placeholder-color);
    font-size: 24rpx;
  }

  // 箭头条
  .u-form-item-arrow-bar {
    margin-top: 10rpx;
    border-radius: 10rpx;
    &.arrow-bar {
      border-color: var(--common-form-border-color)!important;
      padding: 0 28rpx;
      font-size: inherit;
    }
  }
}
