<script>

import http from "./common/request";
import * as msgType from "./common/messageType";
import * as enums from "./common/enums";
import * as wsApi from "./common/wssocket";
import useConfigStore from "@/store/configStore.js";
import { isCanParse } from "./common";
import { getSimplifyText } from "./common/biz";
import * as upgrade from './common/upgrade';
export default {
	data() {
		return {
			isInit: false, // 是否完成初始化
			isInBackGroup: false, // 是否在后台运行
			isExit: false, // 是否已退出
			audioTip: null,
			reconnecting: false, // 正在重连标志
			isPushModule: false, // 是否勾选了unipush
			lastPlayTime: 0,
			playInterval: 1000, // 3 seconds interval
		};
	},
	methods: {
		init() {
			this.isExit = false;
			// 加载数据
			this.loadStore()
				.then(() => {
					// 初始化websocket
					this.initWebSocket();
					this.reportCid();
					this.initUniPush();
					this.isInit = true;
				})
				.catch((e) => {
					console.log(e);
					this.exit();
				});

		},

		initWebSocket() {
			const configStore = useConfigStore();
			let loginInfo = uni.getStorageSync("loginInfo");
			wsApi.init();
			wsApi.connect(configStore.storage_ws, loginInfo.accessToken);
			wsApi.onConnect(() => {
				// 重连成功提示
				if (this.reconnecting) {
					this.reconnecting = false;
					// uni.showToast({
					// 	title: "已重新连接",
					// 	icon: "none",
					// });
				}
				// 加载离线消息
				this.pullPrivateOfflineMessage(this.chatStore.privateMsgMaxId);
				this.pullGroupOfflineMessage(this.chatStore.groupMsgMaxId);
				this.pullSystemOfflineMessage(this.chatStore.systemMsgMaxSeqNo);
				this.pullGroupMemberOfflineMessage();
				this.pullFriendADDOfflineMessage();
			});
			wsApi.onMessage((cmd, msgInfo) => {
				if (cmd == 2) {
					// 异地登录，强制下线
					// uni.showModal({
					// 	content: "您已在其他地方登录，将被强制下线",
					// 	showCancel: false,
					// });

					setTimeout(() => {
						uni.$emit('openModalEvent', {
							title: '',
							content: '您已在其他地方登录，将被强制下线',
							showCancel: false
						});
					}, 500); // 延迟触发，确保 login.vue 已挂载
					this.exit();
				} else if (cmd == 3) {
					// 私聊消息
					this.handlePrivateMessage(msgInfo);
				} else if (cmd == 4) {
					// 群聊消息
					this.handleGroupMessage(msgInfo);
				} else if (cmd == 5) {
					// 系统消息
					this.handleSystemMessage(msgInfo);
				} else if (cmd == 6 && !msgInfo.type) {
					//有好友申请
					this.handleFriendApply(msgInfo)
				} else if (cmd == 7 && !msgInfo.type) {
					//群申请
					this.handleGroupApply(msgInfo)
				}
			});
			wsApi.onClose((res) => {
				console.log("ws断开", res);
				// 重新连接
				this.reconnectWs();
			});
		},

		newFriendApply(applicantId) {

		},

		loadStore() {
			return this.userStore.loadUser().then(() => {
				const promises = [];
				promises.push(this.friendStore.loadFriend());
				promises.push(this.groupStore.loadGroup());
				promises.push(this.chatStore.loadChat());
				promises.push(this.configStore.loadConfig());
				return Promise.all(promises);
			});
		},

		unloadStore() {
			this.friendStore.clear();
			this.groupStore.clear();
			this.chatStore.clear();
			this.configStore.clear();
			this.userStore.clear();
		},

		pullPrivateOfflineMessage(minId) {
			this.chatStore.setLoadingPrivateMsg(true);
			http({
				url: "/message/private/pullOfflineMessage?minId=" + minId,
				method: "GET",
			}).catch(() => {
				this.chatStore.setLoadingPrivateMsg(false);
			});
		},
		pullGroupOfflineMessage(minId) {
			this.chatStore.setLoadingGroupMsg(true);
			http({
				url: "/message/group/pullOfflineMessage?minId=" + minId,
				method: "GET",
			}).catch(() => {
				this.chatStore.setLoadingGroupMsg(false);
			});
		},
		pullSystemOfflineMessage(minSeqNo) {
			this.chatStore.setLoadingSystemMsg(true);
			this.$http({
				url: "/message/system/pullOfflineMessage?minSeqNo=" + minSeqNo,
				method: "GET",
			}).catch(() => {
				this.chatStore.setLoadingSystemMsg(false);
			});
		},
		pullGroupMemberOfflineMessage() {
			this.chatStore.setLoadingSystemMsg(true);
			this.$http({
				url: "/groupMember/pullOfflineMessage",
				method: "GET",
			}).catch(() => {
				this.chatStore.setLoadingSystemMsg(false);
			});
		},
		pullFriendADDOfflineMessage() {
			this.chatStore.setLoadingSystemMsg(true);
			this.$http({
				url: "/friendAdd/pullOfflineMessage",
				method: "GET",
			}).catch(() => {
				this.chatStore.setLoadingSystemMsg(false);
			});
		},

		handlePrivateMessage(msg) {
			// 消息加载标志
			if (msg.type == enums.MESSAGE_TYPE.LOADING) {
				// 不能parse的话，不要继续了
				if (!isCanParse(msg.content)) return;
				this.chatStore.setLoadingPrivateMsg(JSON.parse(msg.content));
				return;
			}
			// 消息已读处理，清空已读数量
			if (msg.type == enums.MESSAGE_TYPE.READED) {
				this.chatStore.resetUnreadCount({
					type: "PRIVATE",
					targetId: msg.recvId,
				});
				return;
			}
			// 消息回执处理,改消息状态为已读
			if (msg.type == enums.MESSAGE_TYPE.RECEIPT) {
				this.chatStore.readedMessage({
					friendId: msg.sendId,
				});
				return;
			}
			// 标记这条消息是不是自己发的
			msg.selfSend = msg.sendId == this.userStore.userInfo.id;
			// 好友id
			let friendId = msg.selfSend ? msg.recvId : msg.sendId;

			this.loadFriendInfo(friendId, (friend) => {
				const chat = {
					type: "PRIVATE",
					targetId: friend.id,
				};

				// 删除全部消息，一般指删除某个id前的所有消息，这样能适配重新登录的人的消息处理
				if (msg.type == enums.MESSAGE_TYPE.DELETE_All) {
					this.chatStore.deleteAllMessage(chat, msg.targetId);
					return;
				}
				// 删除单条消息
				if (msg.type == enums.MESSAGE_TYPE.DELETE) {
					msg.id = msg.targetId;
					this.chatStore.deleteMessage(msg, chat);
					return;
				}
				// 操作红包领取状态
				if (msg.type == enums.MESSAGE_TYPE.RED_ENVELOPE_RECEIVE) {
					this.chatStore.updateMessage({ id: msg.id, content: msg.content }, chat);
					return;
				}

				this.insertPrivateMessage(friend, msg);
			});
		},
		insertPrivateMessage(friend, msg) {
			// 单人视频信令
			if (msgType.isRtcPrivate(msg.type)) {
				// #ifdef MP-WEIXIN
				// 小程序不支持音视频
				return;
				// #endif
				// 被呼叫，弹出视频页面
				let delayTime = 100;
				if (
					msg.type == enums.MESSAGE_TYPE.RTC_SETUP_VOICE ||
					msg.type == enums.MESSAGE_TYPE.RTC_SETUP_VIDEO
				) {
					let mode =
						msg.type == enums.MESSAGE_TYPE.RTC_SETUP_VIDEO ? "video" : "voice";
					let pages = getCurrentPages();
					let curPage = pages[pages.length - 1].route;
					if (curPage != "pages/chat/chat-private-video") {
						const friendInfo = encodeURIComponent(JSON.stringify(friend));
						uni.navigateTo({
							url: `/pages/chat/chat-private-video?mode=${mode}&friend=${friendInfo}&isHost=false`,
						});
						this.createNotifyMessage(friend.showNickName, msg);
						delayTime = 500;
					}
				}
				setTimeout(() => {
					uni.$emit("WS_RTC_PRIVATE", msg);
				}, delayTime);
				return;
			}
			let chatInfo = {
				type: "PRIVATE",
				targetId: friend.id,
				showName: friend.showNickName,
				headImage: friend.headImage,
				headImageUri: friend.headImage,
			};
			// 打开会话
			this.chatStore.openChat(chatInfo);
			// 插入消息
			this.chatStore.insertMessage(msg, chatInfo);
			// 弹出通知栏消息
			this.createNotifyMessage(friend.showNickName, msg);
			// 播放提示音
			this.playAudioTip(msg.type);
		},
		handleGroupMessage(msg) {
			// 消息加载标志
			if (msg.type == enums.MESSAGE_TYPE.LOADING) {
				// 不能parse的话，不要继续了
				if (!isCanParse(msg.content)) return;
				this.chatStore.setLoadingGroupMsg(JSON.parse(msg.content));
				return;
			}
			// 消息已读处理
			if (msg.type == enums.MESSAGE_TYPE.READED) {
				// 我已读对方的消息，清空已读数量
				let chatInfo = {
					type: "GROUP",
					targetId: msg.groupId,
				};
				this.chatStore.resetUnreadCount(chatInfo);
				return;
			}
			// 消息回执处理
			if (msg.type == enums.MESSAGE_TYPE.RECEIPT) {
				let chatInfo = {
					type: "GROUP",
					targetId: msg.groupId,
				};
				// 更新消息已读人数
				let msgInfo = {
					id: msg.id,
					groupId: msg.groupId,
					readedCount: msg.readedCount,
					receiptOk: msg.receiptOk,
				};
				this.chatStore.updateMessage(msgInfo, chatInfo);
				return;
			}
			// 标记这条消息是不是自己发的
			msg.selfSend = msg.sendId == this.userStore.userInfo.id;
			this.loadGroupInfo(msg.groupId, (group) => {
				const chat = {
					type: "GROUP",
					targetId: msg.groupId,
				};

				// 群管理开关更新
				if (msg.type == enums.MESSAGE_TYPE.GROUP_SEND_FILE) {
					// 禁止发文件
					group.isSendFile = msg.openState;
				} else if (msg.type == enums.MESSAGE_TYPE.GROUP_ADD_FRIEND) {
					// 群内互加好友
					group.isAddFriend = msg.openState;
				} else if (msg.type == enums.MESSAGE_TYPE.GROUP_INVITE) {
					// 开启成员邀请
					group.isUserInvite = msg.openState;
				} else if (msg.type == enums.MESSAGE_TYPE.ALL_MEMBER_MUTED) {
					// 全员禁言
					group.isMuted = !!msg.openState;
				} else if (msg.type == enums.MESSAGE_TYPE.SINGLE_MEMBER_MUTED) {
					// 单人禁言
					group.isSingleMuted = !!msg.openState;
				} else if (msg.type == enums.MESSAGE_TYPE.GROUP_SET_GROUP_NAME) {
					// 群成员昵称设置
					group.isUpdateName = msg.openState;
				}

				// 群公告更新
				if (msg.type == enums.MESSAGE_TYPE.GROUP_NOTICE) {
					group.notice = msg.content;
				}

				// 删除全部消息，一般指删除某个id前的所有消息，这样能适配重新登录的人的消息处理
				if (msg.type == enums.MESSAGE_TYPE.DELETE_All) {
					this.chatStore.deleteAllMessage(chat, msg.targetId);
					return;
				}
				// 删除单条消息
				if (msg.type == enums.MESSAGE_TYPE.DELETE) {
					msg.id = msg.targetId;
					this.chatStore.deleteMessage(msg, chat);
					return;
				}
				// 操作红包领取状态
				if (msg.type == enums.MESSAGE_TYPE.RED_ENVELOPE_RECEIVE) {
					this.chatStore.updateMessage({ id: msg.id, content: msg.content }, chat);
					return;
				}

				// 置顶/取消置顶消息
				if (msg.type == enums.MESSAGE_TYPE.TOP) {
					// 为0代表全部信息
					if (msg.id === 0) {
						this.chatStore.updateAllMessage(chat, { isTop: msg.isTop });
					} else {
						this.chatStore.updateMessage({ id: msg.id, isTop: msg.isTop }, chat);
					}
					return;
				}

				// 插入群聊消息
				this.insertGroupMessage(group, msg);
			});
		},
		insertGroupMessage(group, msg) {
			// 群视频信令
			if (msgType.isRtcGroup(msg.type)) {
				// #ifdef MP-WEIXIN
				// 小程序不支持音视频
				return;
				// #endif
				// 被呼叫，弹出视频页面
				let delayTime = 100;
				if (msg.type == enums.MESSAGE_TYPE.RTC_GROUP_SETUP) {
					let pages = getCurrentPages();
					let curPage = pages[pages.length - 1].route;
					if (curPage != "pages/chat/chat-group-video") {
						const userInfos = encodeURIComponent(msg.content);
						const inviterId = msg.sendId;
						const groupId = msg.groupId;
						const url = `/pages/chat/chat-group-video?groupId=${groupId}&isHost=false
												&inviterId=${inviterId}&userInfos=${userInfos}`;
						uni.navigateTo({
							url: url
						});
						this.createNotifyMessage(group.showGroupName, msg);
						delayTime = 500;
					}
				}
				// 消息转发到chat-group-video页面进行处理
				setTimeout(() => {
					uni.$emit("WS_RTC_GROUP", msg);
				}, delayTime);
				return;
			}

			let chatInfo = {
				type: "GROUP",
				targetId: group.id,
				showName: group.showGroupName,
				headImage: group.headImageThumb,
			};
			// 打开会话
			this.chatStore.openChat(chatInfo);
			// 插入消息
			this.chatStore.insertMessage(msg, chatInfo);
			// 弹出通知栏消息
			this.createNotifyMessage(group.showGroupName, msg);
			// 播放提示音 入如果说是提示没必要去发出声音

			this.playAudioTip(msg.type);
		},

		handleSystemMessage(msg) {
			// 消息加载标志
			if (msg.type == this.$enums.MESSAGE_TYPE.LOADING) {
				// 不能parse的话，不要继续了
				if (!isCanParse(msg.content)) return;
				this.chatStore.setLoadingSystemMsg(JSON.parse(msg.content));
				return;
			}
			// 消息已读处理
			if (msg.type == enums.MESSAGE_TYPE.READED) {
				// 我已读对方的消息，清空已读数量
				let chatInfo = {
					type: "SYSTEM",
					targetId: 0,
				};
				this.chatStore.resetUnreadCount(chatInfo);
				return;
			}
			if (msg.type == enums.MESSAGE_TYPE.USER_BANNED) {
				// 用户被封禁
				wsApi.close(3099);
				// uni.showModal({
				// 	content: "您的账号已被管理员封禁，原因:" + msg.content,
				// 	showCancel: false,
				// });

				setTimeout(() => {
					uni.$emit('openModalEvent', {
						title: '',
						content: "您的账号已被管理员封禁，原因:" + msg.content,
						showCancel: false
					});
				}, 500); // 延迟触发，确保 login.vue 已挂载
				this.exit();
				return;
			}
			if (msg.type == enums.MESSAGE_TYPE.USER_LOGOUT) {
				// 用户被登出
				wsApi.close(3099);
				// uni.showModal({
				// 	content: "您的账号已被管理员封禁，原因:" + msg.content,
				// 	showCancel: false,
				// });

				setTimeout(() => {
					uni.$emit('openModalEvent', {
						title: '',
						content: msg.content,
						showCancel: false
					});
				}, 500); // 延迟触发，确保 login.vue 已挂载
				this.exit();
				return;
			}
			if (msg.type == enums.MESSAGE_TYPE.USER_UNREG) {
				// 用户账号已注销
				wsApi.close(3099);
				// uni.showModal({
				// 	content: "您的账号已注销",
				// 	showCancel: false,
				// });

				setTimeout(() => {
					uni.$emit('openModalEvent', {
						title: '',
						content: "您的账号已注销",
						showCancel: false
					});
				}, 500); // 延迟触发，确保 login.vue 已挂载
				this.exit();
				return;
			}
			// 插入消息
			this.insertSystemMessage(msg);
		},
		//好友申请
		handleFriendApply(msg) {
			if (msg.informType == enums.APPLY.ADD && Number(msg.content) > 0) {
				//需要用uniPush
				this.createAddMessage("您有的信息好友申请")
				// 播放提示音
				this.playAudioTip(enums.MESSAGE_TYPE.TEXT);
			}
			this.chatStore.setFriendApplyNum(Number(msg.content));

		},
		//群申请
		handleGroupApply(msg) {
			if (msg.informType == enums.APPLY.ADD && Number(msg.content) > 0) {
				//需要用uniPush
				this.createAddMessage("您有新的群聊申请未通过")
				// 播放提示音
				this.playAudioTip(enums.MESSAGE_TYPE.TEXT);
			}
			this.chatStore.setGroupApplyNum(Number(msg.content));
		},
		insertSystemMessage(msg) {
			let chatInfo = {
				type: "SYSTEM",
				targetId: 0,
				showName: "系统通知",
			};

			// 打开会话
			this.chatStore.openChat(chatInfo);
			// 插入消息
			this.chatStore.insertMessage(msg, chatInfo);
		},
		loadFriendInfo(id, callback) {
			let friend = this.friendStore.findFriend(id);
			if (friend) {
				callback(friend);
			} else {
				http({
					url: `/friend/find/${id}`,
					method: "GET",
				}).then((friend) => {
					// 使用 addFriend 方法，它现在包含了去重逻辑
					this.friendStore.addFriend(friend);
					callback(friend);
				}).catch((error) => {
					console.error('加载好友信息失败:', error);
					// 即使失败也要调用回调，避免阻塞
					callback(null);
				});
			}
		},
		loadGroupInfo(id, callback) {
			let group = this.groupStore.findGroup(id);
			if (group.id) {
				callback(group);
			} else {
				http({
					url: `/group/find/${id}`,
					method: "GET",
				}).then((group) => {
					this.groupStore.addGroup(group);
					callback(group);
				});
			}
		},
		exit() {
			console.log("exit");
			this.isExit = true;
			wsApi.close(3099);
			uni.removeStorageSync("loginInfo");
			uni.reLaunch({
				url: "/pages/login/login",
			});
			this.unloadStore();
		},
		playAudioTip(type) {
			//需要判断下是否播放声音
			if (msgType.isTip(type)) {
				return;
			}
			const currentTime = Date.now();
			if (currentTime - this.lastPlayTime < this.playInterval) {
				// If the interval hasn't passed, don't play
				return;
			}

			if (!this.audioTip) {
				this.audioTip = uni.createInnerAudioContext();
				this.audioTip.sessionCategory = "ambient"
				this.audioTip.src = "/static/audio/tip.mp3";
			}

			this.audioTip.play();
			this.lastPlayTime = currentTime;
		},
		refreshToken(loginInfo) {
			return new Promise((resolve, reject) => {
				if (!loginInfo || !loginInfo.refreshToken) {
					reject();
					return;
				}
				http({
					url: "/refreshToken",
					method: "PUT",
					header: {
						refreshToken: loginInfo.refreshToken,
					},
				})
					.then((newLoginInfo) => {
						uni.setStorageSync("loginInfo", newLoginInfo);
						resolve();
					})
					.catch((e) => {
						reject(e);
					});
			});
		},
		reconnectWs() {
			// 已退出则不再重连
			if (this.isExit) {
				return;
			}
			const configStore = useConfigStore();
			// 记录标志
			this.reconnecting = true;
			// 重新加载一次个人信息，目的是为了保证网络已经正常且token有效
			this.reloadUserInfo()
				.then((userInfo) => {
					// uni.showToast({
					// 	title: "连接已断开，尝试重新连接...",
					// 	icon: "none",
					// });
					//ws 尝试重新连接 顶部loading
					this.chatStore.setLoadingPrivateMsg(true);
					this.userStore.setUserInfo(userInfo);
					// 重新连接
					let loginInfo = uni.getStorageSync("loginInfo");
					wsApi.reconnect(configStore.storage_ws, loginInfo.accessToken);
				})
				.catch(() => {
					// 5s后重试
					setTimeout(() => {
						this.reconnectWs();
					}, 5000);
				});
		},
		reloadUserInfo() {
			return http({
				url: "/user/self",
				method: "GET",
			});
		},
		createAddMessage(content) {
			if (!this.isPushModule) {
				return;
			}

			// 仅在后台时弹出提示
			if (!this.isInBackGroup) {
				return;
			}
			const options = {
				cover: true,
				sound: "system",
				title: content,
				//icon: 'static/tarbar/chat_active.png'
			};
			plus.push.createMessage(content, {}, options);
		},
		createNotifyMessage(title, msg) {
			if (!this.isPushModule) {
				return;
			}
			const content = getSimplifyText(msg) || msg.content;
			// 仅在后台时弹出提示
			if (!content || !this.isInBackGroup) {
				return;
			}
			const options = {
				cover: true,
				sound: "system",
				title: title,
				//icon: 'static/tarbar/chat_active.png'
			};
			plus.push.createMessage(content, {}, options);
		},
		initUniPush() {
			if (!this.isPushModule) {
				return;
			}
			plus.push.setAutoNotification(true);
			plus.push.clear();
			const clientInfo = plus.push.getClientInfo();
			plus.push.addEventListener("click", (message) => {
				const msgInfo = message.payload;
				console.log("click:", message);
				this.onClickNotifyMessage(msgInfo);
			});
			plus.push.addEventListener("receive", (message) => {
				console.log("receive", message);
			});
		},
		onClickNotifyMessage(msgInfo) {
			if (msgInfo.type == enums.MESSAGE_TYPE.RTC_GROUP_SETUP) {
				this.$http({
					url: "/webrtc/group/info?groupId=" + msgInfo.groupId,
					method: "GET",
				}).then((rtcInfo) => {
					if (!rtcInfo.isChating) {
						return uni.showToast({
							title: "通话已结束",
							icon: "none",
						});
					}
					// 进入被呼叫页面
					msgInfo.content = JSON.stringify(rtcInfo.userInfos);
					setTimeout(() => this.handleGroupMessage(msgInfo), 500);
				});
			} else if (
				msgInfo.type == enums.MESSAGE_TYPE.RTC_SETUP_VIDEO ||
				msgInfo.type == enums.MESSAGE_TYPE.RTC_SETUP_VOICE
			) {
				this.$http({
					url: "/webrtc/private/info?uid=" + msgInfo.sendId,
					method: "GET",
				}).then((rtcInfo) => {
					if (!rtcInfo.isChating) {
						return uni.showToast({
							title: "通话已结束",
							icon: "none",
						});
					}
					// 进入被呼叫页面
					setTimeout(() => this.handlePrivateMessage(msgInfo), 500);
				});
			}
		},
		reportCid() {
			// APP需要上报cid，用于离线推送
			if (!this.isPushModule) {
				return;
			}
			const clientInfo = plus.push.getClientInfo();
			const cid = clientInfo.clientid;
			if (cid && cid != "null") {
				console.log("cid:", cid);
				// 上报cid
				http({
					url: "/user/reportCid?cid=" + cid,
					method: "POST",
				});
			} else {
				// 有时cid会拿不到,1s后重试
				setTimeout(() => this.reportCid(), 1000);
			}
		},
		removeCid() {
			if (!this.isPushModule) {
				return;
			}
			http({
				url: "/user/removeCid",
				method: "DELETE",
			});
		},
		closeSplashscreen(delay) {
			// #ifdef APP-PLUS
			// 关闭开机动画
			setTimeout(() => {
				plus.navigator.closeSplashscreen();
			}, delay);
			// #endif
		},
		checkPushModule() {
			// #ifdef APP-PLUS
			if (plus && plus.push) {
				plus.runtime.getProperty(plus.runtime.appid, (info) => {
					console.log("info:", info);
					this.isPushModule =
						info.features.includes("push") || info.features.includes("Push");
					console.log("this.isPushModule", this.isPushModule);
				});
			}
			// #endif
		},
	},
	onLaunch() {
		// H5显示打包时间
		// #ifdef H5
		console.log("buildTime", process.env.BUILD_TIME);
		// #endif

		const configStore = useConfigStore();

		// 暗黑模式切换
		uni.onThemeChange(function (res) {
			// 当跟随系统变化时，进行设置
			configStore.setDarkMode(res.theme);
		});

		// 设置默认深色模式
		configStore.setDefaultDarkMode();

		// 深色模式测试代码
		// configStore.setDarkMode('dark');

		// 登录状态校验
		let loginInfo = uni.getStorageSync("loginInfo");

		// 获取红包配置 - 登录后才获取
		if (loginInfo) {
			configStore.updateRedConfig();
		}

		this.$mountStore();
		// 检测是否打包了unipush模块
		this.checkPushModule();
		// 延迟1s，避免用户看到页面跳转
		this.closeSplashscreen(1000);
		this.refreshToken(loginInfo)
			.then(() => {
				// #ifdef H5
				// 跳转到聊天页
				uni.switchTab({
					url: "/pages/chat/chat",
				});
				// #endif
				// 初始化
				this.init();
				this.closeSplashscreen(0);
			})
			.catch(() => {
				// 跳转到登录页
				uni.reLaunch({
					url: "/pages/login/login",
				});
			});

		// #ifdef APP-PLUS

		// 新版本更新检测
		upgrade.checkAndUpgrade();
		// #endif
	},
	onShow() {
		this.isInBackGroup = false;
		if (this.isPushModule) {
			plus.push.clear();
		}
	},
	onHide() {
		this.isInBackGroup = true;
	},
};
</script>

<style lang="scss">
@import "@/uni_modules/uview-plus/index.scss";
@import "@/im.scss";
@import "@/themes/app.scss";
@import url("./static/icon/iconfont.css");

// #ifdef H5
uni-page-head {
	display: none; // h5浏览器本身就有标题
}

// #endif

.tab-page {
	position: relative;
	display: flex;
	flex-direction: column;
	// #ifdef H5
	height: calc(100vh - var(--tab-bar-height) - var(--nav-bar-height)); // h5平台100vh是包含了底部高度，需要减去
	top: var(--nav-bar-height);
	// #endif

	// #ifndef H5
	height: calc(100vh - var(--status-bar-height) - var(--nav-bar-height) - var(--tab-bar-height)); // app平台还要减去顶部手机状态栏高度
	top: calc(var(--nav-bar-height) + var(--status-bar-height));
	// #endif
	color: var(--text-color);
	background-color: var(--bg);
	font-size: var(--font-size);
	font-family: var(--font-family);
}

.page {
	--page-top: var(--nav-bar-height);
	--page-min-height: calc(100vh - var(--page-top));

	position: relative;

	// 有通知栏 或 有置顶栏时
	&.s-has-bar-notice,
	&.s-has-bar-top-msg {
		--page-top: calc(var(--nav-bar-height) + var(--top-bar-item-height) + var(--top-bar-padding-bottom) + var(--top-bar-border-bottom-height));
	}

	// 有通知栏和置顶栏时
	&.s-has-bar-notice.s-has-bar-top-msg {
		--page-top: calc(var(--nav-bar-height) + var(--top-bar-item-height) + var(--top-bar-padding-bottom) + var(--top-bar-border-bottom-height) + var(--top-bar-item-gap) + var(--top-bar-item-height));
	}

	// #ifdef H5
	min-height: var(--page-min-height);
	top: var(--page-top);
	// #endif
	// #ifndef H5
	// app平台还要减去顶部手机状态栏高度
	min-height: calc(var(--page-min-height) - var(--status-bar-height));
	top: calc(var(--page-top) + var(--status-bar-height));
	// #endif

	&.s-full-page {
		// #ifdef H5
		height: var(--page-min-height);
		// #endif
		// #ifndef H5
		// app平台还要减去顶部手机状态栏高度
		height: calc(var(--page-min-height) - var(--status-bar-height));
		// #endif
	}

	color: var(--text-color);
	background-color: var(--bg);
	font-size: var(--font-size);
	font-family: var(--font-family);
}
</style>