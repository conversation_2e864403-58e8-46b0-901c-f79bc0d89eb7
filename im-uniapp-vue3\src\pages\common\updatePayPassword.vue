<template>
	<page-wrapper>
		<view class="page modify-password">
			<nav-bar back>修改支付密码</nav-bar>

			<!-- 表单内容 -->
			<view class="form-content">
				<!-- 手机号 -->
				<view class="form-item">
					<text class="form-label">手机号</text>
					<view class="form-input-box">
						<input type="text" v-model="phone" disabled maxlength="11" placeholder="请输入手机号"
							placeholder-class="input-placeholder" />
					</view>
				</view>

				<!-- 验证码 -->
				<view class="form-item">
					<text class="form-label">验证码</text>
					<view class="form-input-box code-input-box">
						<input type="text" v-model="yzm" placeholder="请输入验证码" placeholder-class="input-placeholder" />
						<text class="send-code-btn" :class="{ 'disabled': isCountingDown }" @click="getCode">{{ tips
						}}</text>
					</view>
				</view>

				<!-- 新密码 -->
				<view class="form-item">
					<text class="form-label">新密码</text>
					<view class="form-input-box">
						<input type="password" maxlength="6" v-model="newPassword" placeholder="请输入密码"
							placeholder-class="input-placeholder" />
					</view>
				</view>

				<!-- 确认密码 -->
				<view class="form-item">
					<text class="form-label">确认密码</text>
					<view class="form-input-box">
						<input type="password" maxlength="6" v-model="confirmPassword" placeholder="请输入密码"
							placeholder-class="input-placeholder" />
					</view>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-btn" @click="submitModify">确认修改</view>

			<!-- 提示弹窗 -->
			<c-popup-normal ref="resultPopup"></c-popup-normal>
		</view>
	</page-wrapper>
</template>

<script setup>
import { ref, onUnmounted, getCurrentInstance } from "vue";
import { backPayPassword, send } from "@/apis/mine/index";
const THIS = getCurrentInstance().proxy;
// 表单数据
const phone = ref(THIS.userStore.userInfo.phone);
const yzm = ref('');
const newPassword = ref('');
const confirmPassword = ref('');


// 倒计时相关
const countdown = ref(0);
const timer = ref(null);
const isCountingDown = ref(false);
const tips = ref('发送验证码');

// 弹窗引用
const resultPopup = ref(null);

// 获取验证码
const getCode = () => {
	// 验证手机号
	if (!phone.value) {
		uni.showToast({
			title: '请输入手机号',
			icon: 'none'
		});
		return;
	}

	// 验证手机号格式
	if (!/^1\d{10}$/.test(phone.value)) {
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		});
		return;
	}

	// 如果正在倒计时，不能再次发送
	if (isCountingDown.value) {
		return;
	}

	// 发送验证码
	sendVerificationCode();
};

// 开始倒计时
const startCountdown = () => {
	isCountingDown.value = true;
	countdown.value = 60;
	tips.value = `${countdown.value}s后重新获取`;

	timer.value = setInterval(() => {
		countdown.value--;
		tips.value = `${countdown.value}s后重新获取`;
		if (countdown.value <= 0) {
			clearInterval(timer.value);
			timer.value = null;
			tips.value = "发送验证码";
			isCountingDown.value = false;
		}
	}, 1000);
};

// 发送验证码
const sendVerificationCode = () => {
	// 显示加载中
	uni.showLoading({
		title: '发送中'
	});

	const params = {
		phone: phone.value
	};
	// 调用发送验证码接口
	send(params).then(() => {
		uni.hideLoading();
		uni.showToast({
			title: '验证码已发送',
			icon: 'success'
		});

		// 开始倒计时
		startCountdown();
	}).catch(err => {
		uni.hideLoading();
		uni.showToast({
			title: err.message || '验证码发送失败',
			icon: 'none'
		});
	});
};

// 提交修改
const submitModify = () => {
	// 表单验证
	if (!phone.value) {
		uni.showToast({
			title: '请输入手机号',
			icon: 'none'
		});
		return;
	}

	if (!yzm.value) {
		uni.showToast({
			title: '请输入验证码',
			icon: 'none'
		});
		return;
	}

	if (!newPassword.value) {
		uni.showToast({
			title: '请输入新密码',
			icon: 'none'
		});
		return;
	}

	if (!confirmPassword.value) {
		uni.showToast({
			title: '请输入确认密码',
			icon: 'none'
		});
		return;
	}

	if (newPassword.value !== confirmPassword.value) {
		uni.showToast({
			title: '两次密码输入不一致',
			icon: 'none'
		});
		return;
	}

	// 显示加载中
	uni.showLoading({
		title: '提交中'
	});

	const params = {
		yzm: yzm.value,
		payPassword: newPassword.value,
	}
	// 调用修改密码接口
	backPayPassword(params).then(() => {
		uni.hideLoading();
		// 显示成功提示
		resultPopup.value.open({
			title: '修改成功',
			content: '支付密码修改成功',
			align: 'center',
			success: () => {
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1000);
			}
		});
	}).catch(err => {
		uni.hideLoading();
		uni.showToast({
			title: err.message || '密码修改失败',
			icon: 'none'
		});
	});
};

// 组件销毁时清除定时器
onUnmounted(() => {
	if (timer.value) {
		clearInterval(timer.value);
		timer.value = null;
	}
});
</script>

<style lang="scss" scoped>
.modify-password {
	background-color: var(--bg);

	.form-content {
		padding: 30rpx;
	}

	.form-item {
		margin-bottom: 30rpx;

		.form-label {
			display: block;
			font-size: 14px;
			color: var(--text-color-light);
			margin-bottom: 15rpx;
		}

		.form-input-box {
			background-color: var(--bg-input);
			border-radius: 8rpx;
			padding: 25rpx;
			border-radius: 15rpx;

			input {
				width: 100%;
				height: 24px;
				font-size: 14px;
				background-color: transparent;
			}

			&.code-input-box {
				display: flex;
				justify-content: space-between;
				align-items: center;

				input {
					flex: 1;
					margin-right: 10rpx;
				}

				.send-code-btn {
					color: #1989fa;
					font-size: 14px;
					white-space: nowrap;
					padding-left: 20rpx;

					&.disabled {
						color: #999;
						pointer-events: none;
					}
				}
			}
		}
	}

	.input-placeholder {
		color: var(--text-color-light);
		font-size: 14px;
	}

	.submit-btn {
		margin: 60rpx 30rpx;
		background-color: #1989fa;
		color: #fff;
		text-align: center;
		padding: 25rpx 0;
		border-radius: 45rpx; // 更圆润的按钮
		font-size: 16px;
	}
}
</style>
