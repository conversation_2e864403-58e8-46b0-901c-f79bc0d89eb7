<script setup>
import { onLoad, onShow } from '@dcloudio/uni-app';
import { computed, reactive, ref, getCurrentInstance } from 'vue';

import { reqRedEnvelopeCoverList, reqSendRed } from '@/apis/red-packet';
import { reqGroupMembers } from '@/apis/group';
import { mapsRedPacketType } from '@/configs/maps/red-packet';
import { getFormatMoney } from '@/common';

const THIS = getCurrentInstance().proxy;

// 默认备注
const defaultRemark = '恭喜发财，大吉大利';

// 红包类型列表
const redPacketTypeList = Object.values(mapsRedPacketType).map((key) => {
  return {
    id: key.id,
    name: key.title
  };
});

const state = reactive({
  formFields: {
    num: '',
    amount: '',
    remark: ''
  },
  rules: {
    num: [
      {
        validator: (rule, value, callback) => {
          const maxNum = THIS.configStore.redConfig.redMaxNum;
          if(value > maxNum) {
            return callback(`红包个数不能超过${maxNum}个`);
          }
          // if(value > refGroupMembers.value.length) {
          //   return callback(`红包个数不能超过群成员人数`);
          // }
          return callback();
        },
        // blur和change事件触发检验
        trigger: ['blur', 'change'],
      },
    ],
    amount: [
      {
        validator: (rule, value, callback) => {
          const minAmount = THIS.configStore.redConfig.redMinAmount;
          const maxAmount = THIS.configStore.redConfig.redMaxAmount;
          if(value < minAmount) {
            return callback(`红包金额最少${minAmount}元`);
          }
          if(value > maxAmount) {
            return callback(`红包金额不能超过${maxAmount}元`);
          }
          return callback();
        },
        // blur和change事件触发检验
        trigger: ['blur', 'change'],
      },
    ]
  }
});

// 是否私聊
const cpdIsPrivate = computed(() => refChatType.value === 'private');
// 是否群聊
const cpdIsGroup = computed(() => refChatType.value === 'group');
// 当前选择的红包类型
const cpdCurRedPacket = computed(() => mapsRedPacketType[refRedPacketType.value]);
// 总金额
const cpdAmount = computed(() => {
  if (cpdIsGroup.value) {
    // 群聊，普通红包，将输入的num和amount相乘
    if (refRedPacketType.value === 2) {
      return state.formFields.amount * state.formFields.num || '0.00';
    }
  }
  return state.formFields.amount || '0.00';
}, {});

// 会话类型
const refChatType = ref('');
// 目标id
const refTargetId = ref('');
// 表单
const refForm = ref(null);
// 群成员列表
const refGroupMembers = ref([]);
// 支付密码弹层
const refPopupPayPass = ref(null);
// 选择红包类型弹层
const refPopupRedPacketType = ref(null);
// 红包类型(1 手气红包, 2 普通红包, 3 专属红包)，默认-普通红包
const refRedPacketType = ref(2);
// 红包封面列表
const refRedPacketCoverList = ref([]);
// 选择的红包封面
const refChoosedCover = ref();
// 选择的群成员
const refChoosedMember = ref();

// 打开红包类型弹层
function doChooseRedPacketType() {
  refPopupRedPacketType.value.open();
}

// 选择红包类型回调
function onSelectMenuItem(item) {
  refRedPacketType.value = item.id;
  refPopupRedPacketType.value.close();
}

// 取消红包类型回调
function onCancelMenuItem() {
  refPopupRedPacketType.value.close();
}

// 去红包记录页
function goRecord() {
  uni.navigateTo({
    url: '/pages/red-packet/record'
  });
}

// 去选择红包封面页
function goChooseCover() {
  uni.navigateTo({
    url: '/pages/red-packet/covers'
  });
}

// 去选择成员页
function goChooseMember() {
  uni.navigateTo({
    url: `/pages/common/choose-member?id=${refTargetId.value}&type=1`
  });
}

// 处理个数，去掉小数点
function formatNum(value){
  // 判断小数点后大于0位，截取0位
  if (value && value.indexOf('.') > -1) {
    const arr = value.split('.');
    return arr[0];
  }
  return value;
}

// 处理金额格式
function formatAmount(value) {
  // 判断小数点后大于2位，截取2位
  if (value && value.indexOf('.') > -1) {
    const arr = value.split('.');
    if (arr[1].length > 2) {
      return arr[0] + '.' + arr[1].slice(0, 2);
    }
  }
  return value;
}

// 提交
function doSubmit() {
  if (state.formFields.amount === '') {
    uni.showToast({
      title: '请填写金额',
      icon: 'none'
    });
    return;
  }
  // 专属红包 - 选择人员提示
  if(refRedPacketType.value === 3) {
    if(!refChoosedMember.value) {
      uni.showToast({
        title: '请选择专属红包接收人',
        icon: 'none'
      });
      return;
    }
  }
  refForm.value
  .validate()
  .then((res) => {
    refPopupPayPass.value.open();
  })
  .catch((errors) => {
  });
}

// 支付密码输入完成回调
function onPayPassFinish(payPassword) {
  const targetType = cpdIsPrivate.value ? 2 : 1;

  const reqParams = {
    ...state.formFields,
    // 支付密码
    payPassword,
    // 目标类型(1 群组, 2 私聊)
    targetType,
    // 目标ID(群聊ID, 私聊接收人用户ID)
    targetId: refTargetId.value
  };

  // 红包备注默认值处理
  reqParams.remark = reqParams.remark || defaultRemark;

  // 红包类型(1 手气红包, 2 普通红包, 3 专属红包)
  reqParams.type = refRedPacketType.value;
  
  // 专属红包 - 选择人员
  if(refRedPacketType.value === 3) {
    reqParams.exclusiveUserId = refChoosedMember.value.userId;
    // 红包数量-固定为1
    reqParams.num = 1;
  }

  // 私聊时，特殊处理
  if (cpdIsPrivate.value) {
    // 红包数量-固定为1
    reqParams.num = 1;
  }

  // 总金额使用计算后的值
  reqParams.amount = cpdAmount.value;

  // 红包封面
  if (refChoosedCover.value) {
    reqParams.redCoverId = refChoosedCover.value.id;
  }

  // 调用接口
  reqSendRed(reqParams).then((res) => {
    uni.navigateBack();
  });
}

onLoad((options) => {
  refChatType.value = options.type;
  refTargetId.value = options.targetId;
  // 如果是群聊，默认(手气红包)
  if (cpdIsGroup.value) {
    refRedPacketType.value = 1;
  }
  // 如果是群聊，获取一下群成员列表
  if (cpdIsGroup.value) {
    reqGroupMembers(refTargetId.value).then((res) => {
      refGroupMembers.value = res;
    });
  }
  // 获取红包封面列表
  reqRedEnvelopeCoverList().then((res) => {
    refRedPacketCoverList.value = res;
    // 默认选择第一个
    refChoosedCover.value = refRedPacketCoverList.value.records[0];
  });

  // 选择红包封面后
  uni.$on('chooseRedPacketType', function(data){
    refChoosedCover.value = data;
	});
  // 选择群成员后
  uni.$on('chooseMember', function(data){
    refChoosedMember.value = data;
  });
});

onShow(() => {});
</script>

<template>
  <page-wrapper>
    <view class="page m-page-red-packet-send">
      <nav-bar back>
        发红包
        <template #right>
          <view @tap="goRecord"> 红包记录 </view>
        </template>
      </nav-bar>

      <view class="u-main">
        <!-- 红包类型，群聊才有 -->
        <view class="u-red-packet-type" v-if="cpdIsGroup" @tap="doChooseRedPacketType">
          {{ cpdCurRedPacket.title }}
          <sx-svg class="u-icon" name="arrow-bottom" size="24" />
        </view>

        <!-- 表单 -->
        <up-form
          class="m-form"
          labelPosition="left"
          :model="state.formFields"
          :rules="state.rules"
          ref="refForm"
        >
          <!-- 发给谁 -->
          <arrow-bar
            v-if="cpdIsGroup && cpdCurRedPacket.showChooseMember"
            class="u-form-item-arrow-bar"
            title="发给谁"
            border="surround"
            @tap="goChooseMember"
          >
            <template #right>
              <view class="u-arror-userinfo" v-if="refChoosedMember">
                <head-image
                  class="avatar"
                  :id="refChoosedMember.userId"
                  :url="refChoosedMember.headImage"
                  :name="refChoosedMember.showNickName"
                  :size="60"
                ></head-image>
                {{ refChoosedMember?.showNickName }}
              </view>
            </template>
          </arrow-bar>

          <!-- 红包个数 -->
          <up-form-item v-if="cpdIsGroup && cpdCurRedPacket.showRedPacketNum" prop="num">
            <image
              src="@/themes/common/images/red-packet/form-red-icon.png"
              class="u-icon-left"
              mode="widthFix"
            />
            红包个数
            <up-input
              type="number"
              :formatter="formatNum"
              v-model="state.formFields.num"
              maxlength="4"
              border="none"
              placeholder="填写个数"
              inputAlign="right"
            >
              <template #suffix>个</template>
            </up-input>
          </up-form-item>

          <!-- 群人数 -->
          <view
            class="u-form-item-tips"
            v-if="cpdIsGroup && cpdCurRedPacket.showGroupNumber"
          >
            本群共{{ refGroupMembers.length }}人
          </view>

          <!-- 金额 -->
          <up-form-item prop="amount">
            <image
              v-if="refRedPacketType === 1"
              src="@/themes/common/images/red-packet/form-pin.png"
              class="u-icon-left s-pin"
              mode="widthFix"
            />
            {{ cpdCurRedPacket.amountFieldLabel }}
            <up-input
              v-model="state.formFields.amount"
              type="digit"
              :formatter="formatAmount"
              maxlength="10"
              border="none"
              placeholder="¥0.00"
              inputAlign="right"
            >
            </up-input>
          </up-form-item>

          <!-- 备注 -->
          <up-form-item>
            <up-textarea
              v-model="state.formFields.remark"
              :placeholder="defaultRemark"
              border="none"
              height="100%"
              autoHeight
              maxlength="25"
              class="u-cur-textarea"
            ></up-textarea>
          </up-form-item>

          <!-- 封面 -->
          <arrow-bar
            class="u-form-item-arrow-bar"
            title="红包封面"
            :describe_text="refChoosedCover?.coverName"
            border="surround"
            @tap="goChooseCover"
          ></arrow-bar>
        </up-form>

        <!-- 金额预览 -->
        <view class="u-amount-preview">
          <view class="u-sign">¥</view>
          <view class="u-amount">{{ getFormatMoney(cpdAmount) }}</view>
        </view>

        <!-- 按钮 -->
        <view class="u-btn-area">
          <up-button class="u-btn" @tap="doSubmit">塞钱进红包</up-button>
        </view>
      </view>
    </view>

    <!-- 选择红包类型弹层 -->
    <popup-menu
      ref="refPopupRedPacketType"
      :items="redPacketTypeList"
      @select="onSelectMenuItem"
      @cancel="onCancelMenuItem"
    ></popup-menu>

    <!-- 支付密码弹层 -->
    <c-popup-pay-pass
      ref="refPopupPayPass"
      :amount="cpdAmount"
      @finish="onPayPassFinish"
    />
  </page-wrapper>
</template>

<style lang="scss" scoped>
.m-page-red-packet-send {
  .u-main {
    padding: 28rpx 28rpx;

    .u-red-packet-type {
      display: flex;
      align-items: center;
      column-gap: 8rpx;
      font-size: 24rpx;
      color: #d0a65d;
      padding: 0 6rpx;
    }

    .u-cur-textarea {
      line-height: 42rpx;
      font-size: 32rpx;
      padding: 26rpx 0;
      min-height: 120rpx;
      box-sizing: border-box;
      align-items: center;
    }

    // 用户信息
    .u-arror-userinfo{
      display: flex;
      align-items: center;
      column-gap: 12rpx;
    }

    // 金额预览
    .u-amount-preview {
      margin-top: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: var(--font-family-inter);
      font-weight: bold;
      .u-sign {
        font-size: 60rpx;
        line-height: 72rpx;
      }
      .u-amount {
        font-size: 72rpx;
        line-height: 88rpx;
      }
    }

    // 红色按钮
    .u-btn-area {
      margin-top: 54rpx;
    }
    .u-btn {
      width: 344rpx;
      height: 92rpx;
      font-size: 32rpx;
      color: #fff;
      background: #de3a39;
      border-radius: 10rpx;
      border: none;
    }
  }
}
</style>
