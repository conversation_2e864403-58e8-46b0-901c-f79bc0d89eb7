<template>
  <page-wrapper>
    <view class="page friend">
      <!-- <view class="tab-page friend"> -->
      <nav-bar add search @add="onAddNewFriends('/pages/friend/friend-add')" @search="onSearch">好友</nav-bar>
      <view class="nav_bar_box" v-if="showSearch">
        <view class="nav-search">
          <uni-search-bar v-model="searchText" focus radius="100" cancelButton="none" placeholder="点击搜索好友"
            @input="onSearchInput" @clear="onSearchClear"></uni-search-bar>
        </view>
      </view>

      <view class="new_add_box" @click="onAddNewFriends('/pages/friend/newFriend')">
        <view class="img_descrip">
          <sx-svg name="new_friend" class="img" size="80" />
          <!-- <image :src="new_friend" class="img" alt="" /> -->
          新的朋友
        </view>

        <view class="num_right">
          <view class="num_box" v-if="friendApplyNum !== 0 && friendApplyNum">{{
            friendApplyNum
          }}</view>
          <uni-icons class="arrow" type="right" size="16"></uni-icons>
        </view>
      </view>

      <view class="new_add_box" @click="onAddNewFriends('/pages/group/newGroup')">
        <view class="img_descrip">
          <sx-svg name="new_group" class="img" size="80" />
          <!-- <image :src="new_group" class="img" alt="" /> -->
          群聊
        </view>
        <view class="num_right">
          <view class="num_box" v-if="groupApplyNum !== 0 && groupApplyNum">{{
            groupApplyNum
          }}</view>
          <uni-icons class="arrow" type="right" size="16"></uni-icons>
        </view>
      </view>

      <view class="friend-tip" v-if="friends.length == 0 && !isLoading">
        温馨提示：您现在还没有任何好友，快点击右上方'+'按钮添加好友吧~
      </view>
      <view v-if="isLoading" class="loading-container">
        <u-loading-icon size="36"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>
      <view class="friend-items" v-else>
        <up-index-list :index-list="friendIdx" :sticky="true" @select="onIndexSelect">
          <template v-for="(friends, i) in friendGroups" :key="friendIdx[i]">
            <up-index-item :name="friendIdx[i]">
              <up-index-anchor :text="friendIdx[i] === '*' ? '在线' : friendIdx[i]"></up-index-anchor>
              <view v-for="(friend, idx) in friends" :key="`friend-${friend.id}-${idx}`">
                <friend-item :friend="friend"></friend-item>
              </view>
            </up-index-item>
          </template>
        </up-index-list>
      </view>
    </view>

    <!-- 底部导航 -->
    <tab-bar :page="$page" />
  </page-wrapper>
</template>

<script>
import { getGroupMap } from "@/common";
// import new_friend from "@/themes/ommonImg/new_friend.png";
// import new_group from "@/themes/ommonImg/new_group.png";

export default {
  data() {
    return {
      // new_group,
      // new_friend,
      showSearch: false,
      searchText: "",
      isLoading: true,
      currentIndex: 0
    };
  },
  methods: {
    onAddNewFriends(rul) {
      uni.navigateTo({
        url: rul,
      });
    },

    onSearch() {
      this.showSearch = !this.showSearch;
      if (!this.showSearch) {
        this.searchText = "";
      }
    },

    onSearchInput(e) {
      this.searchText = e;
      console.log('Search text:', this.searchText);
    },

    onSearchClear() {
      this.searchText = "";
      console.log('Search cleared');
    },

    refreshUnreadBadge(index) {
      let count = 0;
      if (index == 0) {
        count = this.unreadCount;
        this.configStore.unreadCount = count;
      } else {
        count = this.applyNum;
        this.configStore.applyNum = count;
      }
      // Android平台调用原生API设置角标
      // #ifdef APP-PLUS
      if (uni.getSystemInfoSync().platform === "android") {
        plus.runtime.setBadgeNumber(this.unreadCount);
      }
      // #endif
    },

    async initData() {
      try {
        this.isLoading = true;
        await this.friendStore.loadFriend();
      } catch (error) {
        console.error('Load friends error:', error);
      } finally {
        this.isLoading = false;
      }
    },

    onIndexSelect(letter) {
      // 显示当前选中的字母
      uni.showToast({
        title: letter === '*' ? '在线' : letter,
        icon: 'none',
        position: 'center',
        duration: 500
      });
    },

    // 清理重复的好友数据
    cleanupDuplicateFriends() {
      const currentFriends = this.friendStore.friends || [];
      const uniqueFriendsMap = new Map();

      // 使用 Map 去重，保留最新的好友信息
      currentFriends.forEach(friend => {
        if (friend && friend.id) {
          uniqueFriendsMap.set(friend.id, friend);
        }
      });

      const uniqueFriends = Array.from(uniqueFriendsMap.values());

      // 如果发现重复数据，则更新 store
      if (uniqueFriends.length !== currentFriends.length) {
        console.log(`清理重复好友数据：从 ${currentFriends.length} 个减少到 ${uniqueFriends.length} 个`);
        this.friendStore.setFriends(uniqueFriends);
      }
    }
  },
  computed: {
    applyNum() {
      return this.chatStore.friendApplyNum + this.chatStore.groupApplyNum;
    },

    friendApplyNum() {
      return this.chatStore.friendApplyNum;
    },

    groupApplyNum() {
      return this.chatStore.groupApplyNum;
    },

    friends() {
      const friends = this.friendStore.friends || [];
      // 额外的去重保护，以防 store 中仍有重复数据
      const uniqueFriendsMap = new Map();
      friends.forEach(friend => {
        uniqueFriendsMap.set(friend.id, friend);
      });
      return Array.from(uniqueFriendsMap.values());
    },
    friendGroupMap() {

      const searchText = this.searchText?.trim().toLowerCase() || '';
      const filteredFriends = this.friends.filter(friend => {
        if (!searchText) return true;


        const nickName = friend.nickName?.toLowerCase() || '';
        const showNickName = friend.showNickName?.toLowerCase() || '';
        const remarkNickName = friend.remarkNickName?.toLowerCase() || '';

        const isMatch = nickName.includes(searchText) ||
          showNickName.includes(searchText) ||
          remarkNickName.includes(searchText);

        if (isMatch) {
        }

        return isMatch;
      });


      return getGroupMap({
        sourceArray: filteredFriends,
        searchText: this.searchText,
        isOnline: true,
      });
    },
    friendIdx() {
      const keys = Array.from(this.friendGroupMap.keys());
      // 确保在线分组（*）排在最前面，其他按字母顺序排序
      return keys.sort((a, b) => {
        if (a === '*') return -1;
        if (b === '*') return 1;
        return a.localeCompare(b, 'zh');
      });
    },
    friendGroups() {
      // 按照 friendIdx 的顺序返回分组
      return this.friendIdx.map(key => this.friendGroupMap.get(key));
    },
    unreadCount() {
      let count = 0;
      this.chatStore.chats.forEach((chat) => {
        if (!chat.delete) {
          count += chat.unreadCount;
        }
      });
      return count;
    },
  },

  async onLoad() {
    // 页面加载时就开始获取数据
    this.initData();
  },

  onShow() {
    this.refreshUnreadBadge(0);
    this.refreshUnreadBadge(1);
    // 页面显示时，清理可能的重复数据
    this.cleanupDuplicateFriends();
  },

  watch: {
    searchText() {
      this.currentIndex = 0;
    }
  },

  mounted() {
  }
};
</script>

<style lang="scss" scoped>
.friend {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100vh;
  height: calc(100vh - constant(safe-area-inset-bottom) - 100rpx);
  height: calc(100vh - env(safe-area-inset-bottom) - 100rpx);
  padding-bottom: env(safe-area-inset-bottom);

  :deep(.u-index-anchor) {
    height: 60rpx !important;
    background-color: unset !important;
    border-bottom: none !important;
  }

  :deep(.u-index-anchor__text) {
    color: var(--text-color) !important;
  }

  :deep(.u-index-list__letter) {
    top: 60% !important;
    transform: translateY(-50%);
    padding-right: 10px;
  }

  :deep(.u-index-list__letter__item) {
    width: 30rpx !important;
    height: 30rpx !important;
    margin: 1rpx 0 !important;
  }

  :deep(.u-index-list__letter__item__index) {
    font-size: 20rpx !important;
  }

  :deep(.uni-searchbar__box) {
    background-color: var(--bg-card) !important;
  }

  .friend-items {
    flex: 1;
    padding: 0;
    overflow-y: auto;
    position: relative;
    height: calc(100vh - 200rpx - constant(safe-area-inset-bottom));
    height: calc(100vh - 200rpx - env(safe-area-inset-bottom));
    padding-bottom: 100rpx;
  }

  :deep(.uni-input-input) {
    color: var(--text-color) !important;
  }

  .new_add_box {
    height: 90rpx;
    padding: 10rpx;
    padding-left: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--card-border-bottom-color);

    .img_descrip {
      display: flex;
      font-size: 16px;
      align-items: center;

      .img {
        width: 2.625rem;
        height: 2.625rem;
        margin-right: 25rpx;
      }
    }

    .num_right {
      display: flex;

      .num_box {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: red;
        color: var(--text-color);
        border-radius: 50%;
      }

      .arrow {
        margin-left: 10rpx;
        color: var(--text-color-light) !important;
      }
    }
  }

  .friend-tip {
    position: absolute;
    top: 400rpx;
    padding: 50rpx;
    text-align: center;
    line-height: 50rpx;
    color: var(--text-color-lighter);
  }

  .nav_bar_box {
    background-color: var(--bg);
  }

  .loading-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-text {
      margin-top: 20rpx;
      color: var(--text-color-light);
      font-size: var(--font-size-sm);
    }
  }

  :deep(.uni-indexed-list__menu) {
    top: 60% !important;
    transform: translateY(-50%);
    padding-right: 10px;
  }

  :deep(.uni-indexed-list__menu-item) {
    padding: 1rpx 6rpx !important;
    font-size: 20rpx !important;
  }

  :deep(.uni-indexed-list__menu-text) {
    font-size: 20rpx !important;
  }
}
</style>