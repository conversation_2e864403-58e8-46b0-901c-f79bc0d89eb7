<script setup>
import { computed, ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

import { reqGroupOwnerTransfer, reqSetGroupAdmin } from '@/apis/group';
import { mapChooseMemberType } from '@/configs/maps/choose-member';

const refChooseMemberType = ref();
const refGroupId = ref('');
const refSearchText = ref('');
const refCPopupNormal = ref(null);

// 当前选择成员类型
const cpdCurChooseMember = computed(() => mapChooseMemberType[refChooseMemberType.value]);

onLoad((options) => {
  refGroupId.value = options.id;
  refChooseMemberType.value = Number(options.type);
});

// 单选模式回调
function onMemberClick(member) {
  // 普通选择完回上一页
  if(refChooseMemberType.value === 1){
    uni.$emit('chooseMember', member);
    uni.navigateBack({
      delta: 1
    });
    return;
  }
  // 群主转让
  if(refChooseMemberType.value === 2){
    doOwnerTransfer(member);
    return;
  }
}

// 多选模式回调
function onMemberSelect(members) {
  // 选择管理员
  if(refChooseMemberType.value === 3){
    doChooseAdmin(members);
    return;
  }
}

// ---------- 点击群成员回调方法 ----------
// 群主转让
function doOwnerTransfer(member) {
  const data = {
    content: `确定选择'${member.showNickName}'为新的群主，你将自动放弃群主身份?`,
    success: () => {
      reqGroupOwnerTransfer({
        groupId: refGroupId.value,
        userId: member.userId
      }).then(() => {
        uni.navigateBack({
          delta: 1
        });
      });
    }
  };
  refCPopupNormal.value.open(data);
}
// 选择管理员
function doChooseAdmin(members) {
  const reqs = [];
  members.forEach((member) => {
    const { userId } = member;
    // 设置管理员
    reqs.push(
      reqSetGroupAdmin({
        groupId: refGroupId.value,
        userId,
        type: 1
      })
    );
  });

  Promise.all(reqs).then(() => {
    uni.navigateBack({
      delta: 1
    });
  });
}
</script>

<template>
  <page-wrapper>
    <view class="page s-full-page group-choose-member">
      <nav-bar back>{{ cpdCurChooseMember.name }}</nav-bar>
      <view class="nav_bar_box">
        <view class="nav-search">
          <uni-search-bar
            v-model="refSearchText"
            radius="100"
            cancelButton="none"
            placeholder="搜索"
          ></uni-search-bar>
        </view>
      </view>
      <!-- 拼音模式对群成员分页处理有点问题，需要和后端讨论一下拼音模式的群成员分页列表适配方法 -->
      <!-- 先注释 -->
      <!-- isPinyin -->
      <group-members
        :isSelect="cpdCurChooseMember.isSelect"
        :id="refGroupId"
        :searchText="refSearchText"
        :showOnline="false"
        :isSelectJudgeDisable="cpdCurChooseMember.isSelectJudgeDisable"
        @onMemberSelect="onMemberSelect"
        @onMemberClick="onMemberClick"
      />
    </view>
    
    <!-- 普通提示弹层 -->
    <c-popup-normal ref="refCPopupNormal"></c-popup-normal>
  </page-wrapper>
</template>

<style lang="scss" scoped>
:deep(.uni-searchbar__box) {
  background-color: var(--bg-card) !important;
}

:deep(.uni-input-input) {
  color: var(--text-color) !important;
}

.nav_bar_box {
  background-color: var(--bg);
}

.group-choose-member {
  position: relative;
  display: flex;
  flex-direction: column;
}
</style>
