<template>
  <view class="chat-msg-item" :class="[active ? 'active' : '', isSelected ? 'selected' : '']" @click="onItemClick">
    <!-- 选择框 - 在选择模式下显示 -->
    <view class="select-checkbox" v-if="selectMode && !isTip">
      <view class="checkbox-inner" :class="{ 'checked': isSelected }">
        <!-- <text class="iconfont icon-check" v-if="isSelected"></text> -->
        <uni-icons type="checkmarkempty" color="#fff" v-if="isSelected" size="19"></uni-icons>
      </view>
    </view>

    <view class="chat-msg-tip" v-if="isTip">
      <!-- 时间消息单独处理 -->
      <block v-if="msgInfo.type == $enums.MESSAGE_TYPE.TIP_TIME">
        {{ $date.toTimeText(msgInfo.sendTime) }}
      </block>
      <block v-else>
        {{ msgInfo.content }}
      </block>
    </view>
    <view class="chat-msg-normal" v-if="isNormal" :class="{ 'chat-msg-mine': isMe, 'in-select-mode': selectMode }">
      <head-image class="avatar" @longpress.prevent="$emit('longPressHead')" :id="msgInfo.sendId"
        :url="memberInfo.headImage" :name="memberInfo.showName" size="small" :canClick="canClickAvatar"></head-image>
      <view class="chat-msg-content">
        <view v-if="msgInfo.groupId && !isMe" class="chat-msg-top">
          <text>{{ memberInfo.showName }}</text>
        </view>
        <view class="chat-msg-bottom" :class="{
          's-card': msgInfo.type == 5,
          's-red-packet': msgInfo.type == $enums.MESSAGE_TYPE.RED_ENVELOPE_SEND,
          'no-tail': msgInfo.type == 5 || msgInfo.type == $enums.MESSAGE_TYPE.IMAGE || msgInfo.type == $enums.MESSAGE_TYPE.VIDEO || msgInfo.type == $enums.MESSAGE_TYPE.FILE,
        }">

          <long-press-menu v-if="msgInfo.type == $enums.MESSAGE_TYPE.TEXT" :items="menuItems" @select="onSelectMenu">
            <!-- rich-text支持显示表情，但是不支持点击a标签 -->
            <rich-text v-if="$emo.containEmoji(msgInfo.content)" class="chat-msg-text" :nodes="nodesText"></rich-text>
            <!-- up-parse支持点击a标签,但安卓打包后表情无法显示,原因未知 -->
            <up-parse v-else class="chat-msg-text" :showImgMenu="false" :content="nodesText"></up-parse>
          </long-press-menu>

          <!-- 名片 -->
          <view v-if="msgInfo.type == 5" :class="{ 'chat-msg-mine': isMe }" class="shareBusinessCard_box">
            <long-press-menu :items="menuItems" @select="onSelectMenu">
              <view class="card_box" @click.stop="goToUer(msgInfo)">
                <view class="img_name">
                  <head-image :name="JSON.parse(msgInfo.content).nickName" :url="JSON.parse(msgInfo.content).headImage"
                    :size="70"></head-image>
                  <span class="name">{{
                    JSON.parse(msgInfo.content).nickName
                  }}</span>
                </view>
                <view class="text_card">个人名片</view>
              </view>
            </long-press-menu>
          </view>

          <!-- 红包发送 -->
          <long-press-menu :items="menuItems" @select="onSelectMenu"
            v-if="msgInfo.type == $enums.MESSAGE_TYPE.RED_ENVELOPE_SEND">
            <view class="u-red-packet-box" :class="{ 's-received': getJSONParse(msgInfo.content)?.redStatus === 1 }"
              :style="{ 'background': `#f99c3b url(${getJSONParse(msgInfo.content)?.redThumbnail}) no-repeat center center / cover` }"
              @tap.stop="doRedPacket(msgInfo)">
              <view class="u-box-top">
                <image class="u-icon" v-if="getJSONParse(msgInfo.content)?.redStatus === 1"
                  src="@/themes/common/images/red-packet/card-opened.png" mode="widthFix" />
                <image class="u-icon" v-else src="@/themes/common/images/red-packet/card-wait.png" mode="widthFix" />
                <view class="u-title">
                  <!-- 专属红包 -->
                  <block v-if="getJSONParse(msgInfo.content)?.type === 3" class="u-desc">
                    给{{ getJSONParse(msgInfo.content)?.exclusiveUserName }}的红包
                    <!-- 只有未领取的时候才显示红包备注 -->
                    <view class="u-desc" v-if="getJSONParse(msgInfo.content)?.redStatus === 0">
                      {{ getJSONParse(msgInfo.content)?.remark }}
                    </view>
                  </block>
                  <block v-else>
                    {{ getJSONParse(msgInfo.content)?.remark }}
                  </block>
                  <view class="u-desc" v-if="getJSONParse(msgInfo.content)?.redStatus === 1">
                    已被领取
                  </view>
                  <view class="u-desc" v-if="getJSONParse(msgInfo.content)?.redStatus === 2">
                    已过期
                  </view>
                </view>
              </view>
              <view class="u-box-bottom">
                {{ SERIES.name }}红包
              </view>
            </view>
          </long-press-menu>

          <view class="chat-msg-image" v-if="msgInfo.type == $enums.MESSAGE_TYPE.IMAGE">
            <long-press-menu :items="menuItems" @select="onSelectMenu">
              <view class="img-load-box">
                <image class="send-image" mode="heightFix" :src="processedUrl" lazy-load="true"
                  @click.stop="onShowFullImage()">
                </image>

                <!-- <secure-image
                  :imageName="JSON.parse(msgInfo.content).thumbUrl"
                  class="send-image"
                  @click.stop="onShowFullImage"
                /> -->

                <loading v-if="loading"></loading>
              </view>
            </long-press-menu>
            <text title="发送失败" v-if="loadFail" @click="onSendFail"
              class="send-fail iconfont icon-warning-circle-fill"></text>
          </view>
          <view class="chat-msg-video" v-if="msgInfo.type == $enums.MESSAGE_TYPE.VIDEO">
            <long-press-menu :items="menuItems" @select="onSelectMenu">
              <view class="video-load-box">
                <video-player :poster="JSON.parse(msgInfo.content).coverUrl"
                  :url="JSON.parse(msgInfo.content).videoUrl">
                </video-player>
                <loading v-if="loading"></loading>
              </view>
            </long-press-menu>
            <text title="发送失败" v-if="loadFail" @click="onSendFail"
              class="send-fail iconfont icon-warning-circle-fill"></text>
          </view>
          <view class="chat-msg-file" v-if="msgInfo.type == $enums.MESSAGE_TYPE.FILE">
            <long-press-menu :items="menuItems" @select="onSelectMenu">
              <view class="chat-file-box">
                <view class="chat-file-info">
                  <uni-link class="chat-file-name" :text="formatFileName(data.name)" showUnderLine="true"
                    color="#007BFF" :href="data.url"></uni-link>
                  <view class="chat-file-size">{{ fileSize }}</view>
                </view>
                <view class="chat-file-icon iconfont icon-file"></view>
                <loading v-if="loading"></loading>
              </view>
            </long-press-menu>
            <text title="发送失败" v-if="loadFail" @click="onSendFail"
              class="send-fail iconfont icon-warning-circle-fill"></text>
          </view>
          <long-press-menu v-if="msgInfo.type == $enums.MESSAGE_TYPE.AUDIO" :items="menuItems" @select="onSelectMenu">
            <view class="chat-msg-audio chat-msg-text" @click="onPlayAudio()">
              <text class="iconfont icon-voice-play"></text>
              <text class="chat-audio-text">{{
                JSON.parse(msgInfo.content).duration + '"'
              }}</text>
              <text v-if="audioPlayState == 'PAUSE'" class="iconfont icon-play"></text>
              <text v-if="audioPlayState == 'PLAYING'" class="iconfont icon-pause"></text>
            </view>
          </long-press-menu>
          <long-press-menu v-if="isAction" :items="menuItems" @select="onSelectMenu">
            <view class="chat-realtime chat-msg-text" @click="$emit('call')">
              <text v-if="msgInfo.type == $enums.MESSAGE_TYPE.ACT_RT_VOICE" class="iconfont icon-chat-voice"></text>
              <text v-if="msgInfo.type == $enums.MESSAGE_TYPE.ACT_RT_VIDEO" class="iconfont icon-chat-video"></text>
              <text>{{ msgInfo.content }}</text>
            </view>
          </long-press-menu>
          <view class="quote-message" v-if="msgInfo.quoteMessage">
            <long-press-menu ref="quoteMenu" :items="quoteItems" @select="onSelectMenu">
              <chat-quote-message :msgInfo="msgInfo.quoteMessage" :showName="quoteMemberInfo.showName"
                @click="onClickQuoteMessage"></chat-quote-message>
            </long-press-menu>
          </view>
          <view class="chat-msg-status" v-if="!isAction">
            <text class="chat-readed" v-if="
              isMe &&
              !msgInfo.groupId &&
              msgInfo.status == $enums.MESSAGE_STATUS.READED
            ">已读</text>
            <text class="chat-unread" v-if="
              isMe &&
              !msgInfo.groupId &&
              msgInfo.status != $enums.MESSAGE_STATUS.READED
            ">未读</text>
          </view>
          <view class="chat-receipt" v-if="msgInfo.receipt && (isAdmin || isOwner)" @click="onShowReadedBox">
            <text v-if="msgInfo.receiptOk" class="tool-icon iconfont icon-ok"></text>
            <text v-else>{{ msgInfo.readedCount }}人已读</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import SERIES from "@/configs/series";
import { getJSONParse, isCanParse } from "@/common";
import { onDownloadFile, formatFileSize } from "@/common/biz";
import { fetchAndDecrypt } from "../../common/useImageDecrypt";
import { reqMessageDelete, reqMessageTopMsg, reqMessageTopMsgCancel } from "@/apis/message";
import groupStore from "@/store/groupStore";

export default {
  name: "chat-message-item",
  // 注入依赖
  inject: ['getRefCPopupNormal', 'getRefChatGroupReaded', 'getRefCPopupRedPacket'],
  props: {
    // 添加选择模式相关的属性
    selectMode: {
      type: Boolean,
      default: false
    },
    selectedIds: {
      type: Array,
      default: () => [],
    },
    // 当前所在页
    // chat-聊天页 | top-置顶消息页
    curPage: {
      type: String,
      default: "chat",
    },
    chat: {
      type: Object,
      default: () => { },
    },
    msgInfo: {
      type: Object,
      required: true,
    },
    memberInfo: {
      type: Object,
      default: () => { },
    },
    quoteMemberInfo: {
      type: Object,
      default: () => { },
    },
    isOwner: {
      type: Boolean,
      default: false,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    active: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      SERIES,
      audioPlayState: "STOP",
      innerAudioContext: null,
      processedUrl: null,
      groupStore: groupStore(),
    };
  },

  watch: {
    "msgInfo.content": {
      immediate: true, // 组件加载时立即执行一次
      handler(newVal, oldVal) {
        if (!newVal) {
          return;
        }
        this.getProcessedUrl(newVal);
      },
    },
  },

  methods: {
    getJSONParse,
    // 添加选择相关的方法
    onItemClick() {
      this.$emit("choseTransfetMsg", this.msgInfo)
    },

    // 格式化文件名，保留扩展名
    formatFileName(filename) {
      if (!filename) return '';

      // 最大显示长度
      const maxLength = 19;

      // 如果文件名长度小于等于最大长度，直接返回
      if (filename.length <= maxLength) {
        return filename;
      }

      // 查找最后一个点的位置（文件扩展名）
      const lastDotIndex = filename.lastIndexOf('.');

      // 如果没有扩展名或者扩展名就是文件名本身
      if (lastDotIndex === -1 || lastDotIndex === 0) {
        // 没有扩展名，显示前10位 + ...
        return filename.substring(0, 10) + '...';
      }

      // 获取扩展名（包括点）
      const extension = filename.substring(lastDotIndex);

      // 获取文件名主体部分
      const nameBody = filename.substring(0, lastDotIndex);

      // 获取主体部分的最后3个字符
      const lastThreeChars = nameBody.length > 3 ? nameBody.substring(nameBody.length - 3) : nameBody;

      // 截取主体部分的前10位 + ... + 主体部分的最后3个字符 + 扩展名
      return nameBody.substring(0, 10) + '...' + lastThreeChars + extension;
    },

    getProcessedUrl(str) {
      if (!str) {
        return;
      }
      // 不能parse的话，不要继续了
      if (!isCanParse(str)) return;

      const parsedContent = JSON.parse(str);
      if (!parsedContent.thumbUrl) {
        return;
      }
      // 假设加密图片需要移除前 16 个字节
      fetchAndDecrypt(parsedContent.thumbUrl || parsedContent.headImage, 16)
        .then((processedUrl) => {
          //   console.log("处理后的图片地址：", processedUrl);
          this.processedUrl = processedUrl;
        })
        .catch((error) => {
          console.error("处理失败：", error);
        });
    },

    // 点击红包
    doRedPacket(msg) {
      const content = getJSONParse(msg.content);
      const isPrivate = this.chat?.type == "PRIVATE";
      function goDetail() {
        uni.navigateTo({
          url: "/pages/red-packet/detail?id=" + content.redSendOrderNo,
        });
      }
      if (content.redStatus === 0) {
        // 未领取
        // 私聊，且是自己发的情况，跳转到红包详情页
        if (this.isMe && isPrivate) {
          goDetail();
        } else {
          // 打开红包弹层
          this.getRefCPopupRedPacket().open({
            sendUser: this.memberInfo,
            msgInfo: msg,
            msgContent: content,
            chatInfo: this.chat
          });
        }
      } else if (content.redStatus === 1) {
        // 已领取
        goDetail();
      }
    },

    goToUer(msg) {
      // 不能parse的话，不要继续了
      if (!isCanParse(msg.content)) return;
      const val = JSON.parse(msg.content);
      uni.navigateTo({
        url: "/pages/common/user-info?id=" + val.id,
      });
    },

    onSendFail() {
      uni.showToast({
        title: "该文件已发送失败，目前不支持自动重新发送，建议手动重新发送",
        icon: "none",
      });
    },

    onPlayAudio() {
      // 初始化音频播放器
      if (!this.innerAudioContext) {
        this.innerAudioContext = uni.createInnerAudioContext();
        // 不能parse的话，不要继续了
        if (!isCanParse(this.msgInfo.content)) return;
        let url = JSON.parse(this.msgInfo.content).url;
        this.innerAudioContext.src = url;
        this.innerAudioContext.onEnded((e) => {
          this.audioPlayState = "STOP";
          this.emit();
        });
        this.innerAudioContext.onError((e) => {
          this.audioPlayState = "STOP";
          console.log(e);
          this.emit();
        });
      }
      if (this.audioPlayState == "STOP") {
        this.innerAudioContext.play();
        this.audioPlayState = "PLAYING";
      } else if (this.audioPlayState == "PLAYING") {
        this.innerAudioContext.pause();
        this.audioPlayState = "PAUSE";
      } else if (this.audioPlayState == "PAUSE") {
        this.innerAudioContext.play();
        this.audioPlayState = "PLAYING";
      }
      this.emit();
    },

    onSelectMenu(item) {
      // 菜单id转驼峰作为事件key
      let eventKey = item.key
        .toLowerCase()
        .replace(/_([a-z])/g, (g) => g[1].toUpperCase());

      if (eventKey == "download") {
        onDownloadFile(this.msgInfo);
      } else if (eventKey == "downloadMedia") {
        this.onDownloadMedia(this.msgInfo);
      } else if (eventKey == "delete") {
        this.onDeleteMessage(this.msgInfo);
      } else if (eventKey == "topmsg") {
        this.onTopMessage(this.msgInfo);
      } else if (eventKey == "copy") {
        this.onCopyMessage(this.msgInfo);
      } else if (eventKey == "scan") {
        this.onScanImage();
      }
      this.$emit(eventKey, this.msgInfo);
    },

    // 扫描图片中的二维码
    onScanImage() {
      // #ifdef APP-PLUS
      // 不能parse的话，不要继续了
      if (!isCanParse(this.msgInfo.content)) return;

      // 获取图片URL
      const imageData = JSON.parse(this.msgInfo.content);
      const imageUrl = imageData.originUrl || imageData.thumbUrl;
      console.log("imageData", imageData);
      console.log("imageUrl", imageUrl);

      if (!imageUrl) {
        uni.showToast({
          title: "无法获取图片",
          icon: "none"
        });
        return;
      }

      // 显示加载提示
      uni.showLoading({
        title: "正在识别二维码..."
      });

      // 如果是网络图片，先下载到本地再扫描
      if (imageUrl.startsWith('http')) {
        uni.downloadFile({
          url: imageUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              // 下载成功，使用本地临时文件路径扫描
              this.scanLocalImage(res.tempFilePath);
            } else {
              uni.hideLoading();
              console.error("下载图片失败:", res);
              uni.showToast({
                title: "下载图片失败",
                icon: "none"
              });
            }
          },
          fail: (err) => {
            uni.hideLoading();
            console.error("下载图片失败:", err);
            uni.showToast({
              title: "下载图片失败",
              icon: "none"
            });
          }
        });
      } else {
        // 本地图片直接扫描
        this.scanLocalImage(imageUrl);
      }
      // #endif
    },

    // 扫描本地图片中的二维码
    scanLocalImage(localPath) {
      // #ifdef APP-PLUS
      try {
        // 使用plus.barcode进行二维码识别
        plus.barcode.scan(
          localPath,
          (_, result) => {
            uni.hideLoading();
            console.log("扫码结果:", result);

            // 处理扫码结果
            if (!result || !isCanParse(result)) {
              uni.showToast({
                title: "未识别到有效二维码",
                icon: "none"
              });
              return;
            }

            try {
              const msg = JSON.parse(result);
              if (msg.type === "addFriend") {
                // 提取 id
                const id = msg.userId;
                if (id) {
                  uni.navigateTo({
                    url: `/pages/common/user-info?id=${id}`,
                  });
                } else {
                  uni.showToast({
                    title: "未找到有效 ID",
                    icon: "none",
                  });
                }
              } else if (msg.groupId) {
                // 处理加入群聊的二维码
                const groupId = msg.groupId;
                const inviteUserId = msg.inviteUserId || 0;

                if (groupId) {
                  uni.navigateTo({
                    url: `/pages/group/group-qrcode?groupId=${groupId}&inviteUserId=${inviteUserId}&scanMode=true}`,
                  });
                } else {
                  uni.showToast({
                    title: "未找到有效群ID",
                    icon: "none",
                  });
                }
              } else {
                uni.showToast({
                  title: "无效二维码",
                  icon: "none",
                });
              }
            } catch (error) {
              console.error("解析二维码失败:", error);
              uni.showToast({
                title: "解析二维码失败",
                icon: "none"
              });
            }
          },
          (error) => {
            uni.hideLoading();
            console.error("扫码失败:", error);

            // 如果直接扫描失败，尝试使用uni.scanCode
            uni.scanCode({
              scanType: ['qrCode'],
              success: (res) => {
                console.log("scanCode结果:", res.result);
                if (isCanParse(res.result)) {
                  try {
                    const msg = JSON.parse(res.result);
                    if (msg.type === "addFriend" || msg.type === "joinGroup") {
                      // 重新调用onScanImage方法处理结果
                      this.onSelectMenu({
                        key: "SCAN"
                      });
                    } else {
                      uni.showToast({
                        title: "无效二维码",
                        icon: "none"
                      });
                    }
                  } catch (e) {
                    uni.showToast({
                      title: "无效二维码",
                      icon: "none"
                    });
                  }
                } else {
                  uni.showToast({
                    title: "无效二维码",
                    icon: "none"
                  });
                }
              },
              fail: () => {
                uni.showToast({
                  title: "识别二维码失败",
                  icon: "none"
                });
              }
            });
          },
          {
            conserve: true, // 保留原图
            filename: "_doc/barcode/" // 临时保存路径
          }
        );
      } catch (error) {
        uni.hideLoading();
        console.error("扫码过程出错:", error);
        uni.showToast({
          title: "识别二维码失败",
          icon: "none"
        });
      }
      // #endif
    },

    // 删除信息
    onDeleteMessage(msgInfo) {
      const type = this.chat.type.toLowerCase();
      const isGroup = type === "group";
      const matching = isGroup ? "所有人" : this.chat.showName;
      // 群聊默认开启复选框，私聊不开启
      const hasCheckbox = isGroup;
      // 群聊默认勾选复选框，私聊不开启
      const checked = isGroup;
      // 本地删除及成功提示
      const fnCallback = () => {
        this.$emit('onDeleteMessageSuccess', msgInfo.id);
        uni.showToast({
          title: "删除成功",
          icon: "none",
        });
      };
      this.getRefCPopupNormal().open({
        title: "你确定要删除此消息吗？",
        hasCheckbox,
        checked,
        checkBoxName: `同时为"${matching}"撤回`,
        success: (res) => {
          debugger
          // 选择为对方撤回，代表要调用服务端删除
          if (res.checked) {
            // 调用接口删除服务端对应信息
            reqMessageDelete(type, msgInfo.id).then(() => {
              // 因为删除会调用接口，会传递给自己删除本地消息，所以此处不用调用本地删除
              fnCallback();
            });
          } else {
            this.$nextTick(() => {
              // 本地删除
              this.chatStore.deleteMessage(msgInfo, this.chat);
              fnCallback();
            })
          }
        },
      });
    },
    // 置顶/取消置顶消息
    onTopMessage(msgInfo) {
      const isTop = msgInfo.isTop === 1 ? 0 : 1;
      const reqMethod = isTop === 1 ? reqMessageTopMsg : reqMessageTopMsgCancel;
      reqMethod(msgInfo.id).then(() => {
        // 更新群信息
        this.$emit('onTopMessageSuccess', this.chat.targetId, msgInfo.id, isTop);
      });
    },
    // 复制消息
    onCopyMessage(msgInfo) {
      uni.setClipboardData({
        data: msgInfo.content,
        success: () => {
          uni.showToast({ title: "复制成功" });
        },
        fail: () => {
          uni.showToast({ title: "复制失败", icon: "none" });
        },
      });
    },

    onShowFullImage() {
      // 不能parse的话，不要继续了
      if (!isCanParse(this.msgInfo.content)) return;
      let imageUrl = JSON.parse(this.msgInfo.content).originUrl;
      uni.previewImage({
        urls: [imageUrl],
      });
      // uni.previewImage({
      //   urls: [this.processedUrl],
      // });
    },

    // 下载图片或视频
    onDownloadMedia(msgInfo) {
      // 不能parse的话，不要继续了
      if (!isCanParse(msgInfo.content)) return;

      const content = JSON.parse(msgInfo.content);
      let url = '';
      let mediaType = '';

      // 根据消息类型获取正确的URL
      if (msgInfo.type == this.$enums.MESSAGE_TYPE.IMAGE) {
        // 图片优先使用原图URL
        url = content.originUrl || content.thumbUrl;
        mediaType = 'image';
      } else if (msgInfo.type == this.$enums.MESSAGE_TYPE.VIDEO) {
        // 视频使用视频URL
        url = content.videoUrl;
        mediaType = 'video';
      }

      if (!url) {
        uni.showToast({
          title: '无法获取下载地址',
          icon: 'none'
        });
        return;
      }

      // 显示下载中提示
      uni.showLoading({
        title: '下载中...'
      });

      // 下载文件
      uni.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.hideLoading();

            // 根据媒体类型选择保存方法
            if (mediaType === 'image') {
              // 保存图片到相册
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                  });
                },
                fail: (err) => {
                  console.error('保存图片失败:', err);
                  this.handleSaveFailure(err);
                }
              });
            } else if (mediaType === 'video') {
              // 保存视频到相册
              uni.saveVideoToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                  });
                },
                fail: (err) => {
                  console.error('保存视频失败:', err);
                  this.handleSaveFailure(err);
                }
              });
            }
          } else {
            uni.hideLoading();
            uni.showToast({
              title: '下载失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('下载失败:', err);
          uni.hideLoading();
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          });
        }
      });
    },

    // 处理保存失败的情况
    handleSaveFailure(err) {
      // 如果是因为用户拒绝授权导致的失败，提示用户授权
      if (err.errMsg && err.errMsg.indexOf('auth deny') >= 0) {
        uni.showModal({
          title: '提示',
          content: '需要您授权保存到相册',
          success: (res) => {
            if (res.confirm) {
              // 打开设置页面让用户授权
              uni.openSetting();
            }
          }
        });
      } else {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    },

    onShowReadedBox() {
      this.getRefChatGroupReaded().open({
        msgInfo: this.msgInfo
      });
    },
    onClickQuoteMessage() {
      if (!this.$refs.quoteMenu.isShowMenu) {
        this.$emit("locateQuote", this.msgInfo);
      }
    },
    emit() {
      this.$emit("audioStateChange", this.audioPlayState, this.msgInfo);
    },
    stopPlayAudio() {
      if (this.innerAudioContext) {
        this.innerAudioContext.stop();
        this.innerAudioContext = null;
        this.audioPlayState = "STOP";
      }
    },
  },
  computed: {
    // 添加选中状态计算属性
    isSelected() {
      return this.selectedIds.some(item => item.id == this.msgInfo.id);
    },

    mine() {
      return this.userStore.userInfo;
    },
    isMe() {
      return this.msgInfo.selfSend || this.msgInfo.sendId === this.mine.id;
    },
    loading() {
      return this.msgInfo.loadStatus && this.msgInfo.loadStatus === "loading";
    },
    loadFail() {
      return this.msgInfo.loadStatus && this.msgInfo.loadStatus === "fail";
    },
    data() {
      // 不能parse的话，不要继续了
      if (!isCanParse(this.msgInfo.content)) return;
      return JSON.parse(this.msgInfo.content);
    },
    fileSize() {
      return formatFileSize(this.data.size);
    },
    menuItems() {
      let items = [];

      // 为图片和视频类型添加下载选项（放在第一位）
      if (this.msgInfo.type == this.$enums.MESSAGE_TYPE.IMAGE || this.msgInfo.type == this.$enums.MESSAGE_TYPE.VIDEO) {
        items.push({
          key: "DOWNLOAD_MEDIA",
          name: "下载",
          svgName: "download_msg",
        });
      }

      if (this.msgInfo.type == this.$enums.MESSAGE_TYPE.TEXT) {
        items.push({
          key: "COPY",
          name: "复制",
          svgName: "copy_chat_box",
        });
      }
      // 群聊才加的置顶和取消置顶
      if (this.msgInfo.groupId && (this.isAdmin || this.isOwner)) {
        const topText = this.msgInfo.isTop === 1 ? '取消置顶' : '置顶';
        items.push({
          key: "TOPMSG",
          name: topText,
          svgName: this.msgInfo.isTop === 1 ? "pin_top_cancel" : "pin_top",
        });
      }
      // 红包不转发
      if (this.$msgType.isNormal(this.msgInfo.type) && this.msgInfo.type !== this.$enums.MESSAGE_TYPE.RED_ENVELOPE_SEND) {
        items.push({
          key: "FORWARD",
          name: "转发",
          svgName: "transfer_msg",
        });
      }
      if (this.$msgType.isNormal(this.msgInfo.type) && this.curPage === "chat") {
        items.push({
          key: "QUOTE",
          name: "引用",
          svgName: "refer_msg",
        });
      }
      // 在APP环境下，为图片类型添加扫码选项
      // #ifdef APP-PLUS
      if (this.msgInfo.type == this.$enums.MESSAGE_TYPE.IMAGE) {
        items.push({
          key: "SCAN",
          name: "扫码",
          svgName: "scanScan",
        });
      }
      // #endif
      if (this.canDelete) {
        items.push({
          key: "DELETE",
          name: "删除",
          // color: "#e64e4e",
          svgName: "delete_msg",
        });
      }
      if (this.msgInfo.type == this.$enums.MESSAGE_TYPE.FILE) {
        items.push({
          key: "DOWNLOAD",
          name: "下载并打开",
        });
      }
      return items;
    },
    quoteItems() {
      let items = [];
      if (
        this.msgInfo.quoteMessage &&
        this.msgInfo.quoteMessage.status != this.$enums.MESSAGE_STATUS.RECALL
      ) {
        items.push({
          key: "LOCATE_QUOTE",
          name: "定位到原消息",
        });
      }
      return items;
    },
    isTip() {
      return this.$msgType.isTip(this.msgInfo.type);
    },
    isAction() {
      return this.$msgType.isAction(this.msgInfo.type);
    },
    isNormal() {
      const type = this.msgInfo.type;
      return this.$msgType.isNormal(type) || this.$msgType.isAction(type);
    },
    nodesText() {
      let color = this.isMe ? "white" : "";
      let text = this.$url.replaceURLWithHTMLLinks(this.msgInfo.content, color);
      // 将换行符转换为 HTML 换行标签
      text = text.replace(/\n/g, "<br/>");
      return this.$emo.transform(text, "emoji-normal");
    },

    canDelete() {
      if (this.curPage == 'top') { return false }
      if (!this.msgInfo.groupId) return true; // 私聊消息都可以删除
      return (
        this.isMe || // 自己发送的消息
        this.isOwner || // 群主
        this.isAdmin
      ); // 管理员
    },

    // 是否可以点击头像（根据群内互加好友设置）
    canClickAvatar() {
      // 如果是私聊，或者是自己的消息，始终可以点击
      if (!this.msgInfo.groupId || this.isMe) return true;

      // 如果是群聊，根据群设置和用户权限判断
      // 群主和管理员始终可以点击
      if (this.isOwner || this.isAdmin) return true;

      // 获取当前群组信息
      const groupStore = this.groupStore;
      const group = groupStore.findGroup(this.msgInfo.groupId);

      // 获取群设置中的isAddFriend属性
      const isAddFriend = group?.isAddFriend === 1;

      console.log('群内互加好友设置:', group?.isAddFriend, '是否可点击:', isAddFriend);

      // 如果群设置了允许互加好友，则可以点击头像
      if (isAddFriend) return true;

      // 否则不允许点击头像
      return false;
    },
  },
};
</script>

<style scoped lang="scss">
@import '@/themes/common/mixin.scss';

.active {
  background: var(--bg-active);
}

.chat-msg-item {
  padding: 15rpx 20rpx;
  position: relative; // 添加相对定位

  // 添加选中状态样式
  &.selected {
    background-color: rgba(0, 0, 0, 0.05);
  }

  // 选择框样式 - 放在右侧
  .select-checkbox {
    position: absolute;
    right: 25rpx; // 修改为右侧定位
    top: 50%;
    transform: translateY(-50%);
    width: 40rpx;
    height: 40rpx;
    z-index: 10;

    .checkbox-inner {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 2rpx solid var(--border-transfer);
      background-color: var(--bg-model);

      &.checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }
    }
  }

  .chat-msg-tip {
    line-height: 60rpx;
    text-align: center;
    color: var(--text-color-lighter);
    font-size: var(--font-size-smaller-extra);
    padding: 10rpx;
    word-break: break-all;
  }

  .chat-msg-normal {
    position: relative;
    padding-left: 110rpx;
    min-height: 80rpx;

    .avatar {
      position: absolute;
      left: 0;
    }

    .chat-msg-content {
      text-align: left;

      .chat-msg-top {
        display: flex;
        flex-wrap: nowrap;
        color: var(--text-color-lighter);
        font-size: var(--font-size-smaller);
        line-height: var(--font-size-smaller);
        height: var(--font-size-smaller);
      }

      .chat-msg-bottom {
        position: relative;
        display: inline-block;
        padding-right: 80rpx;
        margin-top: 5rpx;

        &:after {
          content: "";
          position: absolute;
          left: -11rpx;
          top: 35rpx;
          width: 36rpx;
          height: 16rpx;
          background-color: var(--chat-message-she-bg); // 和气泡一致
          transform: rotate(45deg); // 形成对角线三角形
        }

        &.s-card:after {
          background-color: var(--bg-share-card);
          transform: rotate(45deg);
        }

        // 红包
        &.s-red-packet:after {
          background-color: #f99c3b;
          left: -11rpx;
          transform: rotate(45deg);
        }

        /* 图片和名片消息不显示气泡尾巴 */
        &.no-tail:after {
          content: none;
        }

        .chat-msg-text {
          position: relative;
          z-index: 2;
          line-height: 1.6;
          margin-top: 10rpx;
          padding: 16rpx 24rpx;
          background-color: var(--chat-message-she-bg);
          border-radius: 20rpx;
          color: var(--text-color);
          font-size: var(--font-size);
          text-align: left;
          display: inline-flex;
          word-break: break-all;
          white-space: pre-line;
          overflow: visible;
        }

        // 红包
        .u-red-packet-box {
          background: #f99c3b;
          border-radius: 10rpx;
          color: #fff;
          width: 448rpx;

          // 已领取状态
          &.s-received {
            opacity: 1;
          }

          .u-box-top {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            padding: 20rpx 24rpx 20rpx;
            column-gap: 20rpx;
            overflow: hidden;

            .u-icon {
              width: 62rpx;
              height: 70rpx;
            }

            .u-title {
              font-size: 28rpx;
              line-height: 38rpx;
              flex: 1;
              text-align: left;
              @include ellipsis();

              .u-desc {
                font-size: 20rpx;
                line-height: 30rpx;
              }
            }
          }

          .u-box-bottom {
            padding: 8rpx 24rpx 12rpx;
            text-align: left;
            font-size: 12px;
            line-height: 24rpx;
          }
        }

        .send-fail {
          color: #e60c0c;
          font-size: 50rpx;
          cursor: pointer;
          margin: 0 20rpx;
        }

        .shareBusinessCard_box {
          position: relative;
          padding-top: 5rpx;

          .card_box {
            position: relative;
            z-index: 2;
            width: 215px;
            height: 77px;
            background-color: var(--bg-share-card);
            border-radius: 10rpx;
            box-shadow: 0 4px 8px rgba(20, 23, 22, 0.1), 0 6px 20px rgba(20, 23, 22, 0.1);

            .text_card {
              text-align: left;
              font-size: 10px;
              padding-left: 30rpx;
              padding-top: 13rpx;
              color: var(--text-color-lighter);
            }

            .img_name {
              padding: 10rpx 10rpx 10rpx 20rpx;
              display: flex;
              align-items: center;
              border-bottom: 1px solid var(--border);

              .name {
                margin-left: 15rpx;
              }
            }
          }
        }

        .chat-msg-image {
          display: flex;
          flex-wrap: nowrap;
          flex-direction: row;
          align-items: center;

          .img-load-box {
            position: relative;

            .send-image {
              min-width: 200rpx;
              max-width: 420rpx;
              height: 350rpx;
              cursor: pointer;
              border-radius: 8rpx;
            }
          }
        }

        .chat-msg-video {
          display: flex;
          flex-wrap: nowrap;
          flex-direction: row;
          align-items: center;
          margin-bottom: 5rpx;
          /* 减少底部间距 */

          .video-load-box {
            position: relative;
            margin-bottom: 0;
            /* 移除底部间距 */
          }
        }

        .chat-msg-file {
          display: flex;
          flex-wrap: nowrap;
          flex-direction: row;
          align-items: center;
          cursor: pointer;

          .chat-file-box {
            position: relative;
            display: flex;
            flex-wrap: nowrap;
            align-items: center;
            min-height: 120rpx;
            border-radius: 8rpx;
            padding: 10rpx 30rpx;
            box-shadow: var(--box-shadow-dark);
            background-color: var(--bg-share-card);
            box-shadow: var(--box-shadow-lighter);

            .chat-file-info {
              flex: 1;
              height: 100%;
              text-align: left;
              font-size: 28rpx;
              width: 300rpx;

              .chat-file-name {
                font-weight: 600;
                margin-bottom: 30rpx;
                word-break: break-all;
              }
            }

            .chat-file-icon {
              font-size: 80rpx;
              color: #d42e07;
            }
          }
        }

        .chat-msg-audio {
          display: flex;
          align-items: center;

          .chat-audio-text {
            padding-right: 15rpx;
          }

          .icon-voice-play {
            font-size: 34rpx;
            padding-right: 16rpx;
          }
        }

        .chat-realtime {
          display: flex;
          align-items: center;

          .iconfont {
            font-size: 40rpx;
            padding-right: 16rpx;
          }
        }

        .chat-msg-status {
          line-height: var(--font-size-smaller-extra);
          font-size: var(--font-size-smaller-extra);
          padding-top: 2rpx;

          .chat-readed {
            display: block;
            padding-top: 2rpx;
            color: var(--text-color-lighter);
          }

          .chat-unread {
            color: var(--danger-color);
          }
        }

        .chat-receipt {
          font-size: var(--font-size-smaller);
          color: var(--text-color-lighter);
          font-weight: 600;

          .icon-ok {
            font-size: 20px;
            color: var(--success-color);
          }
        }
      }
    }

    &.chat-msg-mine {
      text-align: right;
      padding-left: 0;
      padding-right: 110rpx;

      .avatar {
        left: auto;
        right: 0;
      }

      &.in-select-mode {
        padding-right: 170rpx; // 增加右侧边距，为选择框和头像留出空间
      }

      // 选择模式下右侧头像位置调整
      &.in-select-mode .avatar {
        right: 60rpx; // 向左移动头像，不与选择框重叠
      }

      .chat-msg-content {
        text-align: right;

        .chat-msg-bottom {
          padding-left: 80rpx;
          padding-right: 0;

          &:after {
            left: auto;
            right: -11rpx;
            background-color: var(--chat-message-me-bg);
            transform: rotate(45deg);
          }

          // 红包
          &.s-red-packet:after {
            background-color: #f99c3b;
            right: -11rpx;
            transform: rotate(45deg);
          }

          .chat-msg-text {
            margin-left: 10px;
            background-color: var(--chat-message-me-bg);
            color: #fff;
          }

          .chat-msg-image {
            flex-direction: row-reverse;
          }

          .chat-msg-video {
            flex-direction: row-reverse;
          }

          .chat-msg-file {
            flex-direction: row-reverse;
          }

          .chat-msg-audio {
            flex-direction: row-reverse;

            .chat-audio-text {
              padding-right: 0;
              padding-left: 8px;
            }

            .icon-voice-play {
              transform: rotateY(180deg);
            }
          }

          .chat-realtime {
            display: flex;
            flex-direction: row-reverse;

            .iconfont {
              transform: rotateY(180deg);
            }
          }
        }
      }
    }
  }
}
</style>