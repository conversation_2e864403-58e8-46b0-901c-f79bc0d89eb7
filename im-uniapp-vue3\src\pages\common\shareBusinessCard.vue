<template>
  <page-wrapper>
    <nav-bar back @confirmCheck="confirmCheck" :checkedIds="checkedIds">选择聊天</nav-bar>

    <view class="body_box page">
      <view class="nav_bar_box">
        <view class="nav-search">
          <uni-search-bar focus="true" radius="100" v-model="searchText" cancelButton="none"
            placeholder="搜索"></uni-search-bar>
        </view>
      </view>

      <view class="member-items">
        <scroll-view class="scroll-bar" scroll-with-animation="true" scroll-y="true" @scrolltolower="loadMore" :style="{height: 'calc(100vh - 180rpx)'}">
          <view v-for="(m, index) in mergedArray" v-show="filteredArrayShow(m)" :key="index">
            <view class="member-item" @click="onSwitchChecked(m)">
              <head-image v-if="!m.ownerId" :name="m.showNickName" :online="m.online" :url="m.headImage"
                :size="90"></head-image>
              <head-image v-else :name="m.showGroupName" :url="m.headImage" :size="90"></head-image>
              <view class="member-name">
                <span v-if="m.ownerId">{{ m.showGroupName }}
                  <span v-if="m.groupMemberNum">({{ m.groupMemberNum }})</span>
                </span>
                <span v-else>{{ m.showNickName }}</span>
              </view>
              <view class="member-checked">
                <radio :checked="m.checked" :disabled="m.locked" @click.stop="onSwitchChecked(m)" />
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <share-to-popup ref="selBox" :members="checkedIds" :maxSize="configStore.webrtc.maxChannel" :userInfo="userInfo"
        @complete="onInviteOk" @onConfirm="onConfirm"></share-to-popup>
    </view>
  </page-wrapper>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {},
      searchText: "",
      checkedIds: [],
      chat: [],
      pageSize: 20,
      currentPage: 1,
      loading: false,
      noMoreData: false,
    };
  },

  computed: {
    mergedArray() {
      const allItems = [...this.friends, ...this.groups];
      if (!this.searchText.trim()) {
        return allItems.slice(0, this.pageSize * this.currentPage);
      }
      return allItems;
    },

    groups() {
      return this.groupStore.groups;
    },

    friends() {
      return this.friendStore.friends;
    },
  },

  onLoad(options) {
    // 准备分享的人
    this.loadUserInfo(options.id);
  },

  onShow() {
    //所有聊天 是个数组
    this.chat = this.chatStore.chats;
  },

  methods: {
    onConfirm(sendTextContent) {
      // if (!this.checkedIds || this.checkedIds.length == 0) {
      //   this.modelContent = "请选择发送对象";
      //   this.showModal = true;
      //   return;
      // }

      //发送给
      let object = this.checkedIds[0];
      //对应的聊天消息
      let msgTo = this.chat.find((item) => item.targetId == object.id);
      //是否群
      let isGroupChat = Boolean(object.ownerId);
      let url = isGroupChat ? "/message/group/send" : "/message/private/send";
      const params = {
        id: this.userInfo.id,
        headImage: this.userInfo.headImageUri,
        nickName: this.userInfo.nickName,
      };
      let msgInfo = {
        //名片传被分享人
        content: JSON.stringify(params),
        [isGroupChat ? "groupId" : "recvId"]: object.id,
        type: 5,
      };
      //发送接口
      this.$http({ url, method: "post", data: msgInfo }).then((m) => {
        uni.showToast({
          title: `分享名片成功`,
          icon: "none",
        });

        m.selfSend = true;
        //如果已经存在在消息页面 则直接插入数据 
        if (msgTo) {
          msgTo["type"] = object.ownerId ? "GROUP" : "PRIVATE";
          this.chatStore.insertMessage(m, msgTo);
          //置顶会话
          this.chatStore.moveTop(this.chatStore.findChatIdx(msgTo));
        } else {
          //消息页面没有此聊天 
          //组装即将添加到消息页面的顶部的数据
          let chatInfo = {
            type: object.ownerId ? "GROUP" : "PRIVATE",
            targetId: object.ownerId ? m.groupId : m.recvId,
            showName: object.ownerId ? object.showGroupName : object.showNickName,
            headImage: object.headImage,
          };

          //打开刚刚组装的数据
          this.chatStore.openChat(chatInfo);

          //组装发送信息
          const params = {
            ...m,
            selfSend: true,
            isTransfer: true,
            isReceipt: object.ownerId ? true : false,
          }
          this.chatStore.insertMessage(params, chatInfo);
        }
      });

      //如果同时发了消息
      if (sendTextContent && !sendTextContent.id && sendTextContent !== '\n') {
        let msgInfo2 = {
          content: sendTextContent,
          [isGroupChat ? "groupId" : "recvId"]: object.id,
          type: 0,
        };
        //调用接口
        this.$http({ url, method: "post", data: msgInfo2 }).then((m) => {
          m.selfSend = true;
          this.chatStore.insertMessage(m, msgTo);
        });
      }

      //选中取消
      this.mergedArray.forEach((member) => {
        member.checked = false;
      });

      //选中置空
      this.checkedIds = [];

      //关闭弹框
      this.$refs.selBox.close();

      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/common/user-info?id=" + this.userInfo.id,
        });
      }, 1500);
    },

    sendMessageRequest(msgInfo) {
      return new Promise((resolve, reject) => {
        // 请求入队列，防止请求"后发先至"，导致消息错序
        this.reqQueue.push({ msgInfo, resolve, reject });
        this.processReqQueue();
      });
    },

    processReqQueue() {
      if (this.reqQueue.length && !this.isSending) {
        this.isSending = true;
        const reqData = this.reqQueue.shift();
        this.$http({
          url: this.messageAction,
          method: "post",
          data: reqData.msgInfo,
        })
          .then((res) => {
            reqData.resolve(res);
          })
          .catch((e) => {
            reqData.reject(e);
          })
          .finally(() => {
            this.isSending = false;
            // 发送下一条请求
            this.processReqQueue();
          });
      }
    },

    loadUserInfo(id) {
      this.$http({
        url: "/user/find/" + id,
        method: "GET",
      }).then((user) => {
        this.userInfo = user;
      });
    },

    confirmCheck() {
      // this.$refs.selBox.init(this.checkedIds, this.checkedIds, []);
      this.$refs.selBox.open();
    },

    filteredArrayShow(m) {
      const keyword = this.searchText.trim();
      return (
        !m.hide &&
        !m.quit &&
        (m.ownerId
          ? m.showGroupName.includes(keyword)
          : m.showNickName.includes(keyword))
      );
    },
    
    loadMore() {
      if (this.loading || this.noMoreData) return;
      
      this.loading = true;
      this.currentPage++;
      
      const allItems = [...this.friends, ...this.groups].filter(m => !m.hide && !m.quit);
      const totalItems = allItems.length;
      
      if (this.currentPage * this.pageSize >= totalItems) {
        this.noMoreData = true;
      }
      
      this.loading = false;
    },

    onInviteOk() {
      console.log(999);
    },

    onSwitchChecked(m) {

      // 先取消所有成员的选中状态
      this.mergedArray.forEach((member) => {
        member.checked = false;
      });

      // 只选中当前成员
      m.checked = true;

      this.checkedIds = [m];
      return;
      if (m.locked) return; // 如果 locked，直接返回

      m.checked = !m.checked; // 切换选中状态

      const checkedSet = new Set(this.checkedIds); // 使用 Set 处理选中项

      if (m.checked) {
        checkedSet.add(m); // 选中，添加到 Set
      } else {
        checkedSet.delete(m); // 取消选中，从 Set 移除
      }

      this.checkedIds = [...checkedSet]; // 更新数组
    },
  },
};
</script>

<style lang="scss" scoped>
:deep(.uni-searchbar__box) {
  background-color: var(--bg-card) !important;
}

.nav_bar_box {
  background-color: var(--bg);
}

:deep(.uni-input-input) {
  color: var(--text-color) !important;
}

.body_box {
  display: flex;
  flex-direction: column;

  .member-items {
    position: relative;
    flex: 1;

    .member-item {
      height: 120rpx;
      display: flex;
      position: relative;
      padding: 0 30rpx;
      align-items: center;
      background-color: var(--bg-card);
      border-bottom: 1px var(--border) solid;
      white-space: nowrap;

      .member-name {
        flex: 1;
        padding-left: 20rpx;
        font-size: 30rpx;
        font-weight: 600;
        line-height: 60rpx;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .scroll-bar {
      height: 100%;
    }
  }
}
</style>