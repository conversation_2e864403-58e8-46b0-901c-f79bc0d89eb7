# 红包模块
1. 获取红包配置
  - 参数包括：红包功能开启状态、红包最小金额、红包最大金额、红包最大数量
  - 已登录时：项目启动就获取
  - 未登录时：登录后获取

2. 红包入口
  - 在聊天页面，私聊/群聊均支持，只要红包配置是开启
  - 打开底部“+”号展开，发现“红包”操作栏
  - 点击“红包”，跳转：红包发送页

3. 红包发送页
  - 分为私聊红包和群聊红包两种情况
  - 私聊红包都是普通红包，只有“单个金额”可选择
  - 群聊红包分为“拼手气红包”、“普通红包”、“专属红包”
    - 顶部可以选择红包类型
    - “拼手气红包”和“普通红包”都显示当前群有多少人，以及“红包人数”输入框
    - “拼手气红包”的输入金额为“总金额”，将直接扣除此金额
    - “普通红包”的输入金额为“单个金额”，扣除的金额为：红包人数 X 单个金额
    - “专属红包”
      - 选择人员后返回本页，显示选择的人员头像和名称
      - 如果不选择人员，提交按钮会提示：请选择专属红包接收人
      - 专属红包生成的红包卡片，会显示：“给XXX的红包”
      - 打开专属红包的领取弹层，如果不是指定人员，会显示：“仅XXX可领取”。指定人员打开，显示正常的红包备注和领取按钮
      - 专属红包领取详情页：待领取状态，显示文案为：“红包金额{value}元，等待对方领取”
    * 注：以下功能将在后续版本发布
      - 红包的输入的数字限制、小数点后2位限制、红包人数个数不能超过群成员人数限制
      - 所有红包金额保留2位小数的格式处理
  - 红包备注顾名思义
  - 红包封面
    - 默认使用红包封面列表第一个。可以选择红包封面，跳转至红包封面页面选择
  - 右上角是“红包记录”入口，将跳转“收到的红包”页面

4. 点击“塞钱进红包”
  - 如果设置了支付密码，将打开支付密码弹层
  - 如果未设置尺幅密码，将提示用户。
    * 注：此处可优化显示提示弹层，引导用户直接跳转至支付密码设置页：看是否有必要
  - 发送成功后，返回聊天页，显示红包卡片
  - 如果选择了封面，红包卡片上会显示对应封面背景

5. 点击红包卡片
  - 如果是私聊，自己点击自己发的红包将进入：“红包领取详情”页
  - 如果是私聊，点击别人发的红包将打开“红包弹层”
    - 红包弹层显示是谁发的红包，并且有点击“开”的按钮
    - 如果选择了封面，红包弹层上会显示对应封面背景
    - 点击“开”，打开红包
    * 注：此处可以考虑参考微信做钱币翻转的等待动画效果：看是否有必要
    - 打开红包成功后，关闭红包弹层，更新各用户的红包领取状态
  - 如果是群聊，自己点击自己或别人发的红包，都显示“红包弹层”
    - 红包弹层显示是谁发的红包，并且有点击“开”的按钮
    - 点击“开”，打开红包
    - 最下面，
      - 如果是拼手气红包，显示“看看大家的手气>”入口，点击可跳转“红包领取详情”页
      - 其他类型红包，显示“查看领取详情>”入口，点击可跳转“红包领取详情”页
  - 红包如果是已领取状态，点击均跳转：“红包领取详情”页
  - 注：群聊红包中，对于领完红包这种情况的处理，因为比较复杂，所以未更新红包状态。将在后续版本处理

6. 红包领取详情页
  - 显示当前红包的发送者、金额、领取情况
  - 右上角可点击更多按钮，显示“收到的红包”、“发送的红包”入口，点击分别跳转对应的红包记录页
  - 红包封面使用当时发送红包时选择的封面

7. 收到的红包页
  - 右上角可点击更多按钮，显示“收到的红包”、“发送的红包”入口，点击分别在此页面显示对应的红包记录
  - 分别显示本用户收到、发送的红包情况，包括金额、数目、手气最佳等数据
  - 下面显示对应的记录列表
  - 点击对应的记录，可以跳转到对应的“红包领取详情”页
