// 公用业务方法
import { reqMessageDeleteAllMsg } from '@/apis/message';
import { reqGroupFind } from '@/apis/group';
import * as enums from '@/common/enums';
import useUserStore from '@/store/userStore.js';
import useGroupStore from '@/store/groupStore.js';
import useChatStore from '@/store/chatStore.js';
import { getJSONParse, isCanParse } from '.';
import SERIES from '@/configs/series';

// 设置默认群聊信息，并且调用群聊数据及时更新pinia
export function setGroupInfo(groupId, cb) {
  if (groupId == null) return;
  // 设置群聊数据为pinia中数据，便于管理
  function setStoreGroupData() {
    const group = useGroupStore().findGroup(groupId);
    cb?.(group);
  }
  setStoreGroupData();
  reqGroupFind(groupId).then((group) => {
    // 更新聊天页面的群聊信息
    useChatStore().updateChatFromGroup(group);
    // 更新群聊信息
    useGroupStore().updateGroup(group);
    setStoreGroupData();
  });
}

// 获取根据类型判断的简化消息
// msg 消息对象
export function getSimplifyText(msg) {
  let content = '';
  switch (msg?.type) {
    case enums.MESSAGE_TYPE.IMAGE:
      content = '[图片]';
      break;
    case enums.MESSAGE_TYPE.VIDEO:
      content = '[视频]';
      break;
    case enums.MESSAGE_TYPE.FILE:
      content = '[文件]';
      break;
    case enums.MESSAGE_TYPE.AUDIO:
      content = '[语音]';
      break;
    case enums.MESSAGE_TYPE.ACT_RT_VOICE:
      content = '[语音通话]';
      break;
    case enums.MESSAGE_TYPE.ACT_RT_VIDEO:
      content = '[视频通话]';
      break;
    case enums.MESSAGE_TYPE.CARD:
      content = '[名片]';
      break;
    case enums.MESSAGE_TYPE.RED_ENVELOPE_SEND:
    case enums.MESSAGE_TYPE.RED_ENVELOPE_RECEIVE:
      const msgContent = getJSONParse(msg.content);
      const remark = msgContent?.remark ? ' ' + msgContent.remark : '';
      content = `[${SERIES.name}红包]${remark}`;
      break;
    case enums.MESSAGE_TYPE.SYSTEM_MESSAGE:
      content = msg.title;
      break;
    case enums.MESSAGE_TYPE.RTC_SETUP_VOICE:
    case enums.MESSAGE_TYPE.RTC_SETUP_VOICE:
      content = '邀请您进行通话';
      break;
    case enums.MESSAGE_TYPE.RTC_GROUP_SETUP:
      content = '邀请您加入通话';
      break;
  }
  return content;
}

// 获取群人数处理
export function getGroupNumber(group) {
  let size = group.groupMemberNum;
  const isAdmin = isGroupAdmin(group);
  const isOwner = isGroupOwner(group);
  // 如果群人数大于1000，并且不是群主和群管理员，显示1000+
  if (size > 1000 && !isAdmin && !isOwner) {
    size = '1000+';
  }
  return size;
}

// 判断是否群主
export function isGroupOwner(group) {
  return group.groupMemberType === -1;
}

// 判断是否群管理员
export function isGroupAdmin(group) {
  return group.groupMemberType === 1;
}

// 删除群聊并返回消息页
export function exitGroup(group) {
  setTimeout(() => {
    uni.switchTab({
      url: '/pages/chat/chat'
    });
    useGroupStore().removeGroup(group.id);
    useChatStore().removeGroupChat(group.id);
  }, 100);
}

export function formatFileSize(size) {
  if (size > 1024 * 1024) {
    return Math.round(size / 1024 / 1024) + 'M';
  }
  if (size > 1024) {
    return Math.round(size / 1024) + 'KB';
  }
  return size + 'B';
}

// 获取显示名称
// 根据消息数据、会话数据和群人员列表数据获取此消息对应的人员信息
export function getUserInfoByMsg(msgInfo, chat, groupMembers) {
  const result = {};
  // 没有消息，不继续
  if (!msgInfo) return result;

  // 没有sendId，无法得知是谁，也不继续
  if (!msgInfo.sendId) return result;

  if (chat?.type == 'GROUP') {
    let member = groupMembers.find((m) => m.userId == msgInfo.sendId);
    result.showName = member?.showNickName || '';
    result.headImage = member?.headImage || '';
  } else {
    const isMe = msgInfo.selfSend || msgInfo.sendId === getMine().id;
    if (isMe) {
      result.showName = getMine().nickName;
      result.headImage = getMine().headImageThumb;
    } else {
      result.showName = chat.showName;
      result.headImage = chat.headImage;
    }
  }
  return result;
}

// 获取自己
export function getMine() {
  return useUserStore().userInfo;
}

// 下载文件
export function onDownloadFile(msgInfo) {
  // 不能parse的话，不要继续了
  if (!isCanParse(msgInfo.content)) return;
  let url = JSON.parse(msgInfo.content).url;
  uni.downloadFile({
    url: url,
    success(res) {
      if (res.statusCode === 200) {
        var filePath = encodeURI(res.tempFilePath);
        uni.openDocument({
          filePath: filePath,
          showMenu: true
        });
      }
    },
    fail(e) {
      uni.showToast({
        title: '文件下载失败',
        icon: 'none'
      });
    }
  });
}

// 删除全部消息
export function deleteAllMsg({ chat = {}, refCPopupNormal = {} } = {}) {
  const type = chat.type.toLowerCase();
  const isGroup = type === "group";
  const matching = isGroup ? "所有人" : chat.showName;
  // 群聊默认开启复选框，私聊不开启
  const hasCheckbox = isGroup;
  // 群聊默认勾选复选框，私聊不开启
  const checked = isGroup;
  refCPopupNormal.open({
    title: '你确定要清除聊天记录吗？',
    align: 'center',
    hasCheckbox,
    checked,
    checkBoxName: `同时为“${matching}”清除`,
    success: (res) => {
      deleteAllMsgBase({ includeOthers: res.checked, type, chat });
    }
  });
}

// 删除全部消息 - 底层方法
// includeOthers -> 是否选择为对方撤回
export function deleteAllMsgBase({
  includeOthers = false,
  type,
  chat = {},
  needShowTip = true
} = {}) {
  // 成功提示
  const fnCallback = () => {
    if (needShowTip) {
      uni.showToast({
        title: '清除成功',
        icon: 'none'
      });
    }
  };
  // 选择为对方撤回，代表要调用服务端删除
  if (includeOthers) {
    // 调用接口删除服务端对应信息
    reqMessageDeleteAllMsg(type, chat.targetId).then(() => {
      fnCallback();
    });
  } else {
    // 本地删除
    useChatStore().deleteAllMessage(chat);
    fnCallback();
  }
}

/**
 * 处理扫码功能
 * 统一处理二维码扫描逻辑，支持好友添加和群聊加入
 */
export function handleScan() {
  return new Promise((resolve, reject) => {
    uni.scanCode({
      success: (res) => {
        console.log("扫码结果：", res);
        // 不能parse的话，不要继续了
        if (!isCanParse(res.result)) {
          uni.showToast({
            title: "无效二维码",
            icon: "none",
          });
          reject("无效二维码格式");
          return;
        }

        const msg = JSON.parse(res.result);
        if (msg.type === "addFriend") {
          // 提取 id
          const id = msg.userId;
          if (id) {
            uni.navigateTo({
              url: `/pages/common/user-info?id=${id}`,
            });
            resolve({ type: "addFriend", id });
          } else {
            uni.showToast({
              title: "未找到有效 ID",
              icon: "none",
            });
            reject("未找到有效用户ID");
          }
        } else if (msg.groupId) {
          // 处理加入群聊的二维码
          const groupId = msg.groupId;
          const inviteUserId = msg.inviteUserId || 0;

          if (groupId) {
            uni.navigateTo({
              url: `/pages/group/group-qrcode?groupId=${groupId}&inviteUserId=${inviteUserId}&scanMode=true`,
            });
            resolve({ type: "joinGroup", groupId, inviteUserId });
          } else {
            uni.showToast({
              title: "未找到有效群ID",
              icon: "none",
            });
            reject("未找到有效群ID");
          }
        } else {
          // 不是我们的二维码
          uni.showToast({
            title: "无效二维码",
            icon: "none",
          });
          reject("无效二维码类型");
        }
      },
      fail: (err) => {
        console.error("扫码失败：", err);
        reject(err);
      },
    });
  });
}
