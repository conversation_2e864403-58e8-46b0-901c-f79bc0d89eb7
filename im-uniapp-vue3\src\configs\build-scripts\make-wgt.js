const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const { execSync } = require('child_process');

// 获取项目根目录路径
const rootDir = path.resolve(__dirname, '../../../');
const packageJson = require(path.join(rootDir, 'package.json'));

// 检查是否安装了archiver
try {
  require.resolve('archiver');
} catch (e) {
  console.log('正在安装archiver包...');
  execSync('npm install archiver --save-dev');
  console.log('archiver包安装完成');
}

// 获取应用信息
const appName = packageJson.name || 'im-app';
const appVersion = packageJson.version || '1.0.0';

// 根据环境变量确定正确的 appId
let appId;
const appEnv = process.env.APP_ENV || 'dev';
if (appEnv.startsWith('ycl')) {
  appId = '__UNI__C25CCD6'; // 云畅聊的应用ID
  console.log('使用云畅聊应用ID: ' + appId);
} else {
  appId = '__UNI__8C0F9A0'; // 创信密聊的应用ID
  console.log('使用创信密聊应用ID: ' + appId);
}

// 定义目标目录和文件名
const targetDir = path.join(rootDir, 'unpackage/release');
const wgtFilename = `${appId}.wgt`;
const wgtPath = path.join(targetDir, wgtFilename);

// 确保目标目录存在
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
  console.log(`创建目录: ${targetDir}`);
}

// 删除旧的wgt包
try {
  if (fs.existsSync(wgtPath)) {
    fs.unlinkSync(wgtPath);
    console.log(`已删除旧的wgt包: ${wgtPath}`);
  }

  // 删除目录中所有的wgt文件
  const files = fs.readdirSync(targetDir);
  const wgtFiles = files.filter(file => file.endsWith('.wgt'));

  for (const file of wgtFiles) {
    const filePath = path.join(targetDir, file);
    fs.unlinkSync(filePath);
    console.log(`已删除旧的wgt包: ${filePath}`);
  }
} catch (error) {
  console.warn(`删除旧的wgt包时出错: ${error.message}`);
}

// 创建临时目录用于构建wgt包
const tempDir = path.join(rootDir, 'unpackage/temp');
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
  console.log(`创建临时目录: ${tempDir}`);
}

// 准备wgt包内容
console.log('准备wgt包内容...');

try {
  // 获取环境变量
  const appEnv = process.env.APP_ENV || 'dev';
  console.log(`当前环境: ${appEnv}`);

  // 确定构建输出目录
  let buildDir;
  if (appEnv.startsWith('ycl')) {
    buildDir = path.join(rootDir, 'dist/build/ycl/h5');
    console.log('使用云畅聊构建目录');
  } else {
    buildDir = path.join(rootDir, 'dist/build/h5');
    console.log('使用标准构建目录');
  }

  // 检查构建目录是否存在
  if (!fs.existsSync(buildDir)) {
    console.warn(`警告: 构建目录 ${buildDir} 不存在，请先运行 uni build 命令`);
    console.log('尝试使用项目源文件...');

    // 尝试从项目根目录复制manifest.json
    const manifestPath = path.join(rootDir, 'manifest.json');
    if (fs.existsSync(manifestPath)) {
      const manifestContent = fs.readFileSync(manifestPath, 'utf8');
      fs.writeFileSync(path.join(tempDir, 'manifest.json'), manifestContent);
      console.log('已复制manifest.json到临时目录');
    } else {
      console.warn('警告: 未找到manifest.json文件');
    }

    // 复制src目录到临时目录
    const srcDir = path.join(rootDir, 'src');
    if (fs.existsSync(srcDir)) {
      copyDirectory(srcDir, path.join(tempDir, 'src'));
      console.log('已复制src目录到临时目录');
    } else {
      console.warn('警告: 未找到src目录');
    }

    // 复制static目录到临时目录
    const staticDir = path.join(rootDir, 'static');
    if (fs.existsSync(staticDir)) {
      copyDirectory(staticDir, path.join(tempDir, 'static'));
      console.log('已复制static目录到临时目录');
    } else {
      console.warn('警告: 未找到static目录');
    }

    // 复制其他必要文件
    const otherFiles = ['pages.json', 'main.js', 'App.vue', 'uni.scss'];
    otherFiles.forEach(file => {
      const filePath = path.join(rootDir, file);
      if (fs.existsSync(filePath)) {
        fs.copyFileSync(filePath, path.join(tempDir, file));
        console.log(`已复制${file}到临时目录`);
      }
    });
  } else {
    // 直接复制构建目录的内容到临时目录
    console.log(`从构建目录 ${buildDir} 复制文件...`);
    copyDirectory(buildDir, tempDir);
    console.log('已复制构建目录内容到临时目录');

    // 确保manifest.json存在
    const manifestPath = path.join(tempDir, 'manifest.json');
    if (!fs.existsSync(manifestPath)) {
      console.warn('警告: 构建目录中未找到manifest.json文件');

      // 尝试从项目根目录复制
      let manifestFound = false;

      // 清理JSON字符串，移除注释和处理非标准格式
      function cleanAndCopyManifest(sourcePath, destPath) {
        try {
          let content = fs.readFileSync(sourcePath, 'utf8');
          // 不在这里清理，而是在后面统一处理
          fs.writeFileSync(destPath, content);
          return true;
        } catch (error) {
          console.warn(`复制manifest文件时出错: ${error.message}`);
          return false;
        }
      }

      // 尝试从项目根目录复制标准 manifest.json
      const rootManifestPath = path.join(rootDir, 'manifest.json');
      if (fs.existsSync(rootManifestPath)) {
        if (cleanAndCopyManifest(rootManifestPath, manifestPath)) {
          console.log('已从项目根目录复制manifest.json到临时目录');
          manifestFound = true;
        }
      }

      // 如果没有找到标准 manifest.json，尝试从 src 目录复制
      if (!manifestFound) {
        const srcManifestPath = path.join(rootDir, 'src/manifest.json');
        if (fs.existsSync(srcManifestPath)) {
          if (cleanAndCopyManifest(srcManifestPath, manifestPath)) {
            console.log('已从src目录复制manifest.json到临时目录');
            manifestFound = true;
          }
        }
      }

      // 如果是云畅聊环境，尝试使用 manifest.ycl.json
      if (!manifestFound && appEnv.startsWith('ycl')) {
        const yclManifestPath = path.join(rootDir, 'src/manifest.ycl.json');
        if (fs.existsSync(yclManifestPath)) {
          if (cleanAndCopyManifest(yclManifestPath, manifestPath)) {
            console.log('已从src目录复制manifest.ycl.json到临时目录，并重命名为manifest.json');
            manifestFound = true;
          }
        }
      }

      // 如果是创信密聊环境，尝试使用 manifest.chuangxin.json（如果存在）
      if (!manifestFound && !appEnv.startsWith('ycl')) {
        const cxManifestPath = path.join(rootDir, 'src/manifest.chuangxin.json');
        if (fs.existsSync(cxManifestPath)) {
          if (cleanAndCopyManifest(cxManifestPath, manifestPath)) {
            console.log('已从src目录复制manifest.chuangxin.json到临时目录，并重命名为manifest.json');
            manifestFound = true;
          }
        }
      }

      // 如果仍然没有找到任何 manifest 文件，则退出
      if (!manifestFound) {
        console.error('错误: 无法找到任何可用的manifest.json文件');
        console.error('请确保以下位置之一存在manifest文件:');
        console.error('- ' + rootManifestPath);
        console.error('- ' + path.join(rootDir, 'src/manifest.json'));
        console.error('- ' + path.join(rootDir, 'src/manifest.ycl.json') + ' (云畅聊环境)');
        console.error('- ' + path.join(rootDir, 'src/manifest.chuangxin.json') + ' (创信密聊环境)');
        process.exit(1);
      }
    }
  }
} catch (error) {
  console.error('准备wgt包内容时出错:', error);
  process.exit(1);
}

// 创建一个文件输出流
const output = fs.createWriteStream(wgtPath);
const archive = archiver('zip', {
  zlib: { level: 9 } // 设置压缩级别
});

// 监听所有存档数据写入完成
output.on('close', function () {
  console.log(`wgt包创建成功: ${wgtPath}`);
  console.log(`总大小: ${archive.pointer()} 字节`);

  // 清理临时目录
  try {
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log('已清理临时目录');
  } catch (error) {
    console.warn('清理临时目录时出错:', error);
  }
});

// 监听警告
archive.on('warning', function (err) {
  if (err.code === 'ENOENT') {
    console.warn('警告:', err);
  } else {
    throw err;
  }
});

// 监听错误
archive.on('error', function (err) {
  throw err;
});

// 将输出流管道连接到文件
archive.pipe(output);

// 清理JSON字符串，移除注释和处理非标准格式
function cleanJsonString(jsonStr) {
  // 移除注释 (单行和多行)
  let cleaned = jsonStr.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

  // 确保属性名使用双引号
  cleaned = cleaned.replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3');

  // 移除尾部逗号
  cleaned = cleaned.replace(/,(\s*[}\]])/g, '$1');

  return cleaned;
}

// 在打包前确保版本号正确
try {
  const manifestPath = path.join(tempDir, 'manifest.json');
  if (fs.existsSync(manifestPath)) {
    console.log('检查并更新manifest.json中的版本号...');

    // 读取manifest.json
    let manifestContent = fs.readFileSync(manifestPath, 'utf8');

    // 清理JSON内容
    console.log('清理manifest.json内容，移除注释和处理非标准格式...');
    manifestContent = cleanJsonString(manifestContent);

    // 解析JSON
    let manifest;
    try {
      manifest = JSON.parse(manifestContent);
    } catch (parseError) {
      console.error('解析manifest.json失败:', parseError.message);
      console.error('请确保manifest.json是有效的JSON格式');
      console.error('尝试手动修复manifest.json文件后重试');
      process.exit(1);
    }

    // 记录原始版本号
    const originalVersionName = manifest.versionName;
    const originalVersionCode = manifest.versionCode;

    console.log(`当前版本号: ${originalVersionName} (${originalVersionCode})`);

    // 确保版本号存在且合理
    if (!manifest.versionName || !manifest.versionCode) {
      console.warn('警告: manifest.json中缺少版本号信息，将使用默认值');
      manifest.versionName = manifest.versionName || '1.0.0';
      manifest.versionCode = manifest.versionCode || 100;
    }

    // 将更新后的manifest写回文件
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
    console.log(`使用版本号: ${manifest.versionName} (${manifest.versionCode})`);
  }
} catch (error) {
  console.warn('更新版本号时出错:', error.message);
  console.warn('将继续使用原始manifest.json文件');
}

// 将临时目录中的所有文件添加到存档
archive.directory(tempDir, false);

// 完成存档
archive.finalize();

// 辅助函数：复制目录
function copyDirectory(source, destination) {
  // 创建目标目录
  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination, { recursive: true });
  }

  // 读取源目录中的所有文件和子目录
  const entries = fs.readdirSync(source, { withFileTypes: true });

  // 遍历并复制每个条目
  for (const entry of entries) {
    const sourcePath = path.join(source, entry.name);
    const destPath = path.join(destination, entry.name);

    if (entry.isDirectory()) {
      // 递归复制子目录
      copyDirectory(sourcePath, destPath);
    } else {
      // 复制文件
      fs.copyFileSync(sourcePath, destPath);
    }
  }
}
