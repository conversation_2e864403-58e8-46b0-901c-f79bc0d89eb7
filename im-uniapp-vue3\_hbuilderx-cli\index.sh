#!/bin/bash

# 关闭cli
# cli app quit

# 打开cli
cli open

# 相对于脚本文件所在目录的 cli 路径
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

# 定义配置文件
# 创信密聊
config_chuangxin_beta=$SCRIPT_DIR/configs/chuangxin/beta.json
config_chuangxin_prod=$SCRIPT_DIR/configs/chuangxin/prod.json
config_chuangxin_android_beta=$SCRIPT_DIR/configs/chuangxin/android_beta.json
config_chuangxin_android_prod=$SCRIPT_DIR/configs/chuangxin/android_prod.json
config_chuangxin_ios_beta=$SCRIPT_DIR/configs/chuangxin/ios_beta.json
config_chuangxin_ios_prod=$SCRIPT_DIR/configs/chuangxin/ios_prod.json

# 云畅聊
config_ycl_beta=$SCRIPT_DIR/configs/ycl/beta.json
config_ycl_prod=$SCRIPT_DIR/configs/ycl/prod.json
config_ycl_android_beta=$SCRIPT_DIR/configs/ycl/android_beta.json
config_ycl_android_prod=$SCRIPT_DIR/configs/ycl/android_prod.json
config_ycl_ios_beta=$SCRIPT_DIR/configs/ycl/ios_beta.json
config_ycl_ios_prod=$SCRIPT_DIR/configs/ycl/ios_prod.json

if [ $1 == 'chuangxin_beta' ]; then
  config_path=$config_chuangxin_beta
elif [ $1 == 'chuangxin_prod' ]; then
  config_path=$config_chuangxin_prod
elif [ $1 == 'chuangxin_android_beta' ]; then
  config_path=$config_chuangxin_android_beta
elif [ $1 == 'chuangxin_android_prod' ]; then
  config_path=$config_chuangxin_android_prod
elif [ $1 == 'chuangxin_ios_beta' ]; then
  config_path=$config_chuangxin_ios_beta
elif [ $1 == 'chuangxin_ios_prod' ]; then
  config_path=$config_chuangxin_ios_prod

elif [ $1 == 'ycl_beta' ]; then
  config_path=$config_ycl_beta
elif [ $1 == 'ycl_prod' ]; then
  config_path=$config_ycl_prod
elif [ $1 == 'ycl_android_beta' ]; then
  config_path=$config_ycl_android_beta
elif [ $1 == 'ycl_android_prod' ]; then
  config_path=$config_ycl_android_prod
elif [ $1 == 'ycl_ios_beta' ]; then
  config_path=$config_ycl_ios_beta
elif [ $1 == 'ycl_ios_prod' ]; then
  config_path=$config_ycl_ios_prod

else
  echo "请输入参数"
  exit 1
fi

echo $config_path

# 通过配置文件打包
cli pack --config $config_path