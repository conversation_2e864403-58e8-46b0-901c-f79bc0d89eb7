<template>
	<page-wrapper>
		<view class="page wallet-page">
			<nav-bar back :lastText="lastText" @onClickLastText="onClickLastText()">我的钱包</nav-bar>

			<view class="body_box">
				<!-- 总资产卡片 -->
				<view class="asset-card">
					<view class="asset-title">总资产</view>
					<view class="asset-amount">¥{{ balanceValue }}</view>
					<view class="action-buttons">
						<view class="action-btn recharge-btn" @click="onRecharge">
							充值 <uni-icons type="right" class="right_icon" size="14" color="#FFFFFF"></uni-icons>
						</view>
						<view class="action-btn transfer-btn" @click="onTransfer">
							划转 <uni-icons type="right" class="right_icon" size="14" color="#FFFFFF"></uni-icons>
						</view>
					</view>
				</view>

				<!-- 划转记录 -->
				<view class="transfer-records">
					<view class="records-title">划转记录</view>

					<!-- 表头 -->
					<view class="records-header">
						<text class="header-account">账号</text>
						<text class="header-amount">划转金额</text>
						<text class="header-status">划转申请</text>
						<text class="header-time">划转时间</text>
					</view>

					<!-- 记录列表 -->
					<view class="records-list">
						<no-data v-if="dataList.length === 0"></no-data>
						<view v-else>
							<view v-for="(item, index) in dataList" :key="index" class="record-item">
								<text class="item-account">{{ item.accountNo || 'HHHHHH' }}</text>
								<text class="item-amount">{{ item.amount || 1000 }}</text>
								<text class="item-status" :class="getStatusClass(item.status)">
									{{ getStatusText(item.status) }}
								</text>
								<text class="item-time">{{ item.createTime || '2023-04-17 18:03:25' }}</text>
							</view>
						</view>

						<!-- 加载状态提示 -->
						<view v-if="dataList.length > 0" class="loading-status">
							<view v-if="isLoading" class="loading">
								<uni-icons type="spinner-cycle" size="18" color="var(--text-color-light)"></uni-icons>
								<text class="loading-text">加载中...</text>
							</view>
							<view v-else-if="!hasMore" class="no-more">
								没有更多数据了
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 充值弹窗 -->
			<c-popup-normal ref="rechargePopup"></c-popup-normal>
		</view>
	</page-wrapper>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from "vue";
import { balance, page, self } from "@/apis/mine/index";
import { onShow, onReachBottom } from "@dcloudio/uni-app";

const THIS = getCurrentInstance().proxy;

// 根据用户是否设置了支付密码来显示不同的文本
const lastText = computed(() => {
	// 使用 THIS.userStore 中的最新用户信息
	return THIS.userStore.userInfo.isSetPassword ? '修改支付密码' : '设置支付密码';
});

// 弹窗引用
const rechargePopup = ref(null);
const balanceValue = ref(0.00);
const dataList = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const hasMore = ref(false);
const isLoading = ref(false);

// 获取划转记录数据
const getDataRecord = (isLoadMore = false) => {
	// 设置加载状态
	isLoading.value = true;

	// 首次加载时显示全屏加载
	if (!isLoadMore) {
		uni.showLoading({
			title: '加载中'
		});
	}

	const params = {
		page: currentPage.value,
		size: pageSize.value
	};

	page(params).then(res => {
		if (isLoadMore) {
			// 加载更多时，追加数据
			dataList.value = [...dataList.value, ...res.records];
		} else {
			// 首次加载，直接赋值
			dataList.value = res.records;
		}

		// 更新总数和是否有更多数据
		total.value = res.total;
		hasMore.value = dataList.value.length < total.value;

		// 隐藏加载中状态
		isLoading.value = false;
		if (!isLoadMore) {
			uni.hideLoading();
		}
	}).catch(err => {
		console.error('获取划转记录失败', err);
		isLoading.value = false;
		if (!isLoadMore) {
			uni.hideLoading();
		}
		uni.showToast({
			title: '获取划转记录失败',
			icon: 'none'
		});
	});
};

// 页面滚动到底部时触发
onReachBottom(() => {
	// 如果正在加载或没有更多数据，则不执行加载
	if (isLoading.value || !hasMore.value) return;

	// 页码加1并加载更多数据
	currentPage.value++;
	getDataRecord(true);
});

// 获取钱包余额
const getBalance = () => {
	balance().then(res => {
		balanceValue.value = res.money;
	}).catch(err => {
		console.error('获取余额失败', err);
		uni.showToast({
			title: '获取余额失败',
			icon: 'none'
		});
	});
};

const getSelf = () => {
	// 更新用户信息
	THIS.userStore.loadUser();
};

// 获取状态文本
const getStatusText = (status) => {
	switch (status) {
		case 0:
			return '待审核';
		case 1:
			return '已通过';
		case 2:
			return '已拒绝';
		default:
			return '未知状态';
	}
};

// 获取状态样式类
const getStatusClass = (status) => {
	switch (status) {
		case 0:
			return 'status-waiting';
		case 1:
			return 'status-success';
		case 2:
			return 'status-fail';
		default:
			return '';
	}
};

// 充值按钮点击事件
const onRecharge = () => {
	rechargePopup.value.open({
		title: '充值提示',
		content: '请联系客服进行充值操作',
		align: 'center'
	});
};

// 划转按钮点击事件
const onTransfer = () => {
	// 检查余额是否为0
	if (balanceValue.value <= 0) {
		uni.showToast({
			title: '总资产不足，请充值再试',
			icon: 'none',
			duration: 1500
		});
		return;
	}

	// 检查用户是否已设置支付密码
	if (THIS.userStore.userInfo.isSetPassword) {
		// 已设置支付密码，直接跳转到划转页面
		uni.navigateTo({
			url: '/pages/common/walletTransfer'
		});
	} else {
		// 未设置支付密码，先跳转到设置支付密码页面
		uni.showToast({
			title: '请先设置支付密码',
			icon: 'none',
			duration: 1500
		});

		// 延迟跳转，让用户看到提示
		setTimeout(() => {
			uni.navigateTo({
				url: '/pages/common/setPayPassword'
			});
		}, 1500);
	}
};

// 设置/修改支付密码按钮点击事件
const onClickLastText = () => {
	// 根据用户是否设置了支付密码来跳转到不同的页面
	if (THIS.userStore.userInfo.isSetPassword) {
		// 已设置密码，跳转到修改密码页面
		uni.navigateTo({
			url: '/pages/common/updatePayPassword'
		});
	} else {
		// 未设置密码，跳转到设置密码页面
		uni.navigateTo({
			url: '/pages/common/setPayPassword'
		});
	}
};

onShow(() => {
	getSelf();
	getBalance();
	getDataRecord();
});
</script>

<style scoped lang="scss">
.wallet-page {
	background-color: var(--bg);
	min-height: 100vh;

	.body_box {
		padding: 5rpx 15rpx 15rpx 15rpx;
	}

	// 总资产卡片
	.asset-card {
		margin: 10px;
		background: linear-gradient(to right, #1989fa, #39b9fa);
		border-radius: 8px;
		color: white;
		overflow: hidden;
		margin-bottom: 50rpx;

		.asset-title {
			font-size: 14px;
			padding-top: 15px;
			text-align: center;
			opacity: 0.8;
		}

		.asset-amount {
			font-size: 30px;
			font-weight: bold;
			padding: 20px 0;
			text-align: center;
		}

		.action-buttons {
			display: flex;
			border-top: 1px solid rgba(255, 255, 255, 0.2);

			.action-btn {
				flex: 1;
				text-align: center;
				padding: 13px 0;
				font-size: 14px;
				display: flex;
				align-items: center;
				justify-content: center;

				.right_icon {
					margin-left: 10rpx;
				}

				&.recharge-btn {
					border-right: 1px solid rgba(255, 255, 255, 0.2);
				}
			}
		}
	}

	// 划转记录
	.transfer-records {
		margin: 10px;

		.records-title {
			font-size: 16px;
			font-weight: bold;
			margin-bottom: 10px;
		}

		.records-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 0;
			font-size: 14px;
			color: var(--text-color);
		}

		.records-list {
			.record-item {
				display: flex;
				padding: 12px 0;
				font-size: 14px;
				border-bottom: 1px solid var(--card-border-bottom-color);
			}

			.loading-status {
				margin-top: 10px;

				.loading {
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 15px 0;

					.loading-text {
						margin-left: 8px;
						color: var(--text-color-light);
						font-size: 14px;
					}
				}

				.no-more {
					text-align: center;
					padding: 15px 0;
					color: var(--text-color-light);
					font-size: 12px;
				}
			}
		}

		.header-account,
		.item-account {
			flex: 1;
			text-align: center;
		}

		.header-amount,
		.item-amount {
			flex: 1;
			text-align: center;
		}

		/* 账号和金额共享的样式 */
		.item-account,
		.item-amount {
			word-break: break-all;
			/* 允许在任意字符间断行 */
			word-wrap: break-word;
			/* 允许长单词换行到下一行 */
			white-space: normal;
			/* 空白会被忽略，文本会换行 */
			width: 25%;
			/* 固定宽度为父容器的25% */
			max-width: 25%;
			/* 最大宽度限制 */
			box-sizing: border-box;
			padding: 0 2px;
			font-size: 12px;
			display: inline-block;
		}

		.header-status,
		.item-status {
			flex: 1;
			text-align: center;

			&.status-success {
				color: #2196F3;
			}

			&.status-fail {
				color: #FF5722;
			}

			&.status-waiting {
				color: #FF9800;
			}
		}

		.header-time,
		.item-time {
			flex: 1.5;
			text-align: center;
			font-size: 12px;
		}
	}
}
</style>