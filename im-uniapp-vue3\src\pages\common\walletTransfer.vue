<template>
	<page-wrapper>
		<view class="page wallet-transfer">
			<nav-bar back>划转账户</nav-bar>

			<!-- 划转金额 -->
			<view class="section-title">划转金额</view>

			<view class="box_box">
				<view class="amount-input">
					<text class="currency-symbol">¥</text>
					<input type="digit" v-model="amount" placeholder="0.00" placeholder-class="amount-placeholder"
						@input="onAmountInput" />
					<text class="all-btn" @click="selectAll">全部</text>
				</view>
			</view>

			<!-- 账号输入 -->
			<view class="section-title">请输入账号</view>
			<view class="box_box">
				<view class="input-box">
					<input type="text" v-model="accountNo" placeholder="请输入用户账号"
						placeholder-class="input-placeholder" />
				</view>
			</view>

			<!-- 姓名输入 -->
			<view class="section-title">请输入姓名</view>

			<view class="box_box">
				<view class="input-box">
					<input type="text" v-model="name" placeholder="请输入真实姓名" placeholder-class="input-placeholder" />
				</view>
			</view>

			<!-- 划转内容 -->
			<view class="section-title">划转内容</view>
			<view class="box_box">
				<view class="textarea-box">
					<textarea v-model="transferContent" placeholder="请输入你要备注的内容"
						placeholder-class="input-placeholder" />
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-btn" @click="submitTransfer">提交</view>

			<!-- 提示弹窗 -->
			<c-popup-normal ref="transferPopup"></c-popup-normal>

			<!-- 密码输入弹窗 -->
			<c-popup-pay-pass ref="passwordPopup" :amount="amount" @close="onPasswordPopupClose"
				@finish="onPasswordFinish"></c-popup-pay-pass>
		</view>
	</page-wrapper>
</template>

<script setup>
import { ref } from "vue";
import { balance, transfer } from "@/apis/mine/index";

// 表单数据
const amount = ref('');
const accountNo = ref('');
const name = ref('');
const transferContent = ref('');

// 弹窗引用
const transferPopup = ref(null);
const passwordPopup = ref(null);

// 临时存储划转参数
const transferParams = ref(null);

// 金额输入处理
const onAmountInput = (e) => {
	// 限制只能输入数字和小数点，且只能有一个小数点，小数点后最多两位
	let value = e.detail.value;
	if (value) {
		// 替换多个小数点为一个小数点
		value = value.replace(/\.+/g, '.');
		// 保留两位小数
		if (value.includes('.')) {
			const parts = value.split('.');
			if (parts[1].length > 2) {
				value = parts[0] + '.' + parts[1].substring(0, 2);
			}
		}
		amount.value = value;
	}
};

// 选择全部金额
const selectAll = () => {
	// 显示加载中
	uni.showLoading({
		title: '获取余额中'
	});

	// 调用获取余额接口
	balance().then(res => {
		uni.hideLoading();
		if (res && res.money) {
			amount.value = res.money.toString();
		} else {
			amount.value = '0.00';
		}
	}).catch(err => {
		uni.hideLoading();
		console.error('获取余额失败', err);
		uni.showToast({
			title: '获取余额失败',
			icon: 'none'
		});
	});
};

// 提交划转申请
const submitTransfer = () => {
	// 表单验证
	if (!amount.value) {
		uni.showToast({
			title: '请输入划转金额',
			icon: 'none'
		});
		return;
	}

	if (!accountNo.value) {
		uni.showToast({
			title: '请输入账号',
			icon: 'none'
		});
		return;
	}

	if (!name.value) {
		uni.showToast({
			title: '请输入姓名',
			icon: 'none'
		});
		return;
	}

	// 准备划转参数
	transferParams.value = {
		amount: parseFloat(amount.value),
		accountNo: accountNo.value,
		name: name.value,
		transferContent: transferContent.value
	};

	// 显示密码输入弹窗
	passwordPopup.value.open(); // 打开密码弹窗
};

// 关闭密码弹窗回调
const onPasswordPopupClose = () => {
	// 密码弹窗关闭时的处理逻辑
	console.log('密码弹窗已关闭');
};

// 密码输入完成回调
const onPasswordFinish = (value) => {
	// 验证密码不能为空
	if (!value) {
		uni.showToast({
			title: '请输入密码',
			icon: 'none'
		});
		return;
	}

	// 关闭密码弹窗
	passwordPopup.value.close(); // 关闭密码弹窗

	// 显示加载中
	uni.showLoading({
		title: '提交中'
	});

	// 添加密码到参数中
	const params = {
		...transferParams.value,
		payPassword: value
	};

	// 调用划转接口
	transfer(params).then(() => {
		uni.hideLoading();
		// 显示成功提示
		transferPopup.value.open({
			title: '划转申请',
			content: '划转申请已提交，请等待审核',
			align: 'center',
			success: () => {
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1000);
			}
		});
	}).catch(err => {
		uni.hideLoading();
		uni.showToast({
			title: err.message || '划转申请提交失败',
			icon: 'none'
		});
	});
};
</script>

<style lang="scss" scoped>
.wallet-transfer {
	// min-height: 100vh;

	.box_box {
		padding: 15px;
	}

	.section-title {
		padding: 15px 15px 10px;
		font-size: 14px;
		color: var(--text-color);
	}

	.amount-input {
		background-color: var(--bg-input);
		border-radius: 15rpx;
		padding: 15px;
		display: flex;
		align-items: center;

		.currency-symbol {
			font-size: 30px;
			font-weight: bold;
			margin-right: 5px;
			color: var(--text-color-light);
		}

		input {
			flex: 1;
			font-size: 30px;
			font-weight: bold;
			height: 40px;
		}

		.amount-placeholder {
			color: var(--text-color-light);
			font-size: 30px;
			font-weight: bold;
		}

		.all-btn {
			color: #1989fa;
			font-size: 14px;
			padding: 5px 10px;
		}
	}

	.input-box {
		background-color: var(--bg-input);
		padding: 15px;
		border-radius: 15rpx;

		input {
			width: 100%;
			height: 24px;
			font-size: 14px;
		}
	}

	.textarea-box {
		background-color: var(--bg-input);
		border-radius: 15rpx;
		padding: 15px;

		textarea {
			width: 100%;
			height: 100px;
			font-size: 14px;
		}
	}

	.input-placeholder {
		color: var(--text-color-light);
		font-size: 14px;
	}

	.submit-btn {
		margin: 30px 15px;
		background-color: #1989fa;
		color: #fff;
		text-align: center;
		padding: 12px 0;
		border-radius: 4px;
		font-size: 16px;
	}
}
</style>
