import request from '@/common/request';

// 获取用户余额
export const balance = () => {
  return request({
    url: `/amountChange/balance`,
    method: 'get',
  });
};

// 获取用户信息
export const self = () => {
  return request({
    url: `/user/self`,
    method: 'get',
  });
};

// 分页查询划转记录
export const page = (data) => {
  return request({
    url: `/amountChange/transfer/record/page`,
    method: 'post',
    data
  });
};

// 划转账户
export const transfer = (data) => {
  return request({
    url: `/amountChange/transfer`,
    method: 'post',
    data
  });
};

// 设置支付密码
export const setPayPassword = (data) => {
  return request({
    url: `/setPayPassword`,
    method: 'post',
    data
  });
};

// 修改支付密码
export const backPayPassword = (data) => {
  return request({
    url: `/backPayPassword`,
    method: 'post',
    data
  });
};

// 获取验证码
export const send = (data) => {
  return request({
    url: `/send`,
    method: 'post',
    data
  });
};

// 设置群互加好友
export const reqSetGroupAddFriend = (data) => {
  return request({
    url: `/group/setGroupAddFriend`,
    method: 'post',
    data
  });
};

// 设置群成员邀请
export const reqSetGroupInvite = (data) => {
  return request({
    url: `/group/setGroupInvite`,
    method: 'post',
    data
  });
};

// 设置群邀请校验
export const reqSetGroupInviteCheck = (data) => {
  return request({
    url: `/group/setGroupInviteCheck`,
    method: 'post',
    data
  });
};

// 设置允许修改群昵称
export const reqSetGroupUpdateName = (data) => {
  return request({
    url: `/group/setGroupUpdateName`,
    method: 'post',
    data
  });
};

// 修改群昵称
export const reqSetGroupNickName = (data) => {
  return request({
    url: `/group/setGroupNickName`,
    method: 'post',
    data
  });
};

// 设置群管理员
export const reqSetGroupAdmin = (data) => {
  return request({
    url: `/group/setGroupAdmin`,
    method: 'post',
    data
  });
};

// 修改群公告
export const reqUpdateNotice = (data) => {
  return request({
    url: `/group/updateNotice`,
    method: 'post',
    data
  });
};

// 一键复制群聊
export const reqGroupCopy = (data) => {
  return request({
    url: `/group/copy`,
    method: 'post',
    data
  });
};

// 获取新群组申请列表（支持分页）
export function reqNewGroups({ page = 1, size = 10 } = {}) {
  return request({
    url: "/groupMember/findGroupAddPage",
    method: "post",
    data: {
      page,
      size
    }
  });
}

// 通过群申请
export function reqPassGroupAdd(id) {
  return request({
    url: `/groupMember/passGroupAdd/${id}`,
    method: "POST"
  });
}

// 拒绝群申请
export function reqRejectGroupAdd(id) {
  return request({
    url: `/groupMember/noPassGroupAdd/${id}`,
    method: "POST"
  });
}
// 检查版本
export function getVersion(curVersion) {
  return request({
    url: '/system/checkVersion?version=' + curVersion,
    method: "GET"
  });
}
