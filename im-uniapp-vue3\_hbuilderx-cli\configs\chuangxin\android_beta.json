// 创信密聊 安卓 测试包
{
  // 项目名字或项目绝对路径
  "project": "im-uniapp-vue3",
  // 打包平台 默认值android  值有"android","ios" 如果要打多个逗号隔开打包平台
  "platform": "android",
  // 是否使用自定义基座 默认值false  true自定义基座 false自定义证书
  "iscustom": false,
  // 打包方式是否为安心打包默认值false,true安心打包,false传统打包
  "safemode": true,
  // android打包参数
  "android": {
    // 安卓包名
    "packagename": "com.chuangxin.beta",
    // 安卓打包类型 默认值0 0 使用自有证书 1 使用公共证书 2 使用老版证书
    "androidpacktype": "2",
    // 安卓使用自有证书自有打包证书参数
    // 安卓打包证书别名,自有证书打包填写的参数
    "certalias": "",
    // 安卓打包证书文件路径,自有证书打包填写的参数
    "certfile": "",
    // 安卓打包证书密码,自有证书打包填写的参数
    "certpassword": "",
    // 安卓平台要打的渠道包 取值有"google","yyb","360","huawei","xiaomi","oppo","vivo"，如果要打多个逗号隔开
    "channels": ""
  },
  // 是否混淆 true混淆 false关闭
  "isconfusion": false,
  // 开屏广告 true打开 false关闭
  "splashads": false,
  // 悬浮红包广告true打开 false关闭
  "rpads": false,
  // push广告 true打开 false关闭
  "pushads": false,
  // 加入换量联盟 true加入 false不加入
  "exchange": false
}