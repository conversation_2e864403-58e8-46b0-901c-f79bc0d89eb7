<template>
  <page-wrapper>
    <view class="page group-qrcode">
      <nav-bar back :lastText="showQRCode ? '保存' : ''" @onClickLastText="saveQrCode" :isWhite="true"></nav-bar>

      <!-- 二维码展示区域 -->
      <view class="body_box" v-if="showQRCode">
        <view class="box_box" id="qrcode-container" ref="qrcodeContainer">
          <view class="img_box">
            <view class="avatar-container">
              <head-image class="img" :name="group.name" :url="group.headImage"></head-image>
              <view class="username">群聊：{{ group.name }}({{ group.groupMemberNum }})</view>
            </view>
          </view>

          <view class="qr_img_box">
            <!-- 使用 uqrcode 组件显示二维码 -->
            <uqrcode ref="uqrcodeRef" canvas-id="qrcode" :value="qrVal"
              :options="{ foregroundColor: selectedColor, size: 200, borderRadius: 5 }"></uqrcode>
          </view>

          <!-- 颜色选择器 -->
          <view class="color-picker">
            <view v-for="(color, index) in colorOptions" :key="index" class="color-option"
              :class="{ active: selectedColor === color }" :style="{ backgroundColor: color }"
              @click="selectedColor = color"></view>
          </view>

          <!-- 使用 getThemeImagesFunc 函数 -->
          <view class="theme-icon" v-if="false">
            <image :src="getThemeImagesFunc('QRcode')" style="width: 0; height: 0; opacity: 0;"></image>
          </view>

          <view class="bottom_text">扫一扫上面的二维码图案，进入群聊</view>
        </view>
      </view>

      <view v-if="!showQRCode">
        <view class="img_box_noshowQRCode">
          <head-image class="img" :name="group.name" :url="group.headImage"></head-image>
          <view class="username">群聊：{{ group.name }}({{ group.groupMemberNum }})</view>
        </view>

        <!-- 扫码后的按钮区域 -->
        <view class="action_buttons">
          <view class="button_row" v-if="userInGroup == 0" @click="sendMessage">
            发消息
          </view>
          <view class="button_row" v-if="userInGroup == 2" @click="joinGroup">
            加入群聊
          </view>
          <view class="button_row" v-if="userInGroup == 1">
            申请中
          </view>
        </view>
      </view>

      <!-- 画布用于生成保存的图片 -->
      <canvas canvas-id="shareCanvas" class="share-canvas"></canvas>
    </view>
  </page-wrapper>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, getCurrentInstance } from 'vue';
import { fetchAndDecrypt } from "@/common/useImageDecrypt";
import getThemeImages from '@/themes/themeImages';
import { reqQRCodeGroupFind, reqQRCodeGroupAdd } from '@/apis/group';
// 导入 uqrcode 组件
import uqrcode from '@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue';

// 导出函数供模板使用
const getThemeImagesFunc = getThemeImages;

const userStore = getCurrentInstance().proxy.userStore;
// 响应式状态
const groupId = ref('');
const inviteUserId = ref(userStore.userInfo.id || '369'); // 邀请人ID - 会在onMounted中设置为当前用户ID
const group = reactive({}); // 群组信息
const selectedColor = ref("#4CAF50"); // 默认使用绿色
const colorOptions = [
  "#000000", // 黑色
  "#1296db", // 蓝色
  "#4CAF50", // 绿色
  "#FF5722", // 橙色
  "#9C27B0", // 紫色
  "#E91E63"  // 粉色
];
const showQRCode = ref(true); // 控制是否显示二维码，true为显示二维码，false为显示按钮
const scanMode = ref(false); // 是否是扫码模式
const processedHeadImage = ref(null); // 存储处理后的头像URL
const uqrcodeRef = ref(null); // 二维码组件引用
const userInGroup = ref(0); // 用户是否已在群内 0未申请 1申请中 2可以加群 默认在

// 计算属性
const qrVal = computed(() => {
  return JSON.stringify({
    groupId: groupId.value,
    inviteUserId: inviteUserId.value
  });
});

// 监听头像变化
watch(() => group.headImage, async (newVal) => {
  if (newVal) {
    try {
      // 处理加密的头像图片
      processedHeadImage.value = await fetchAndDecrypt(newVal, 16);
    } catch (error) {
      console.error('处理头像失败:', error);
      processedHeadImage.value = null;
    }
  } else {
    processedHeadImage.value = null;
  }
}, { immediate: true });

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};

  // 解析参数
  if (options.groupInfo) { //从群组详情页跳转过来的
    const groupInfo = JSON.parse(decodeURIComponent(options.groupInfo));
    console.log('groupInfo:', groupInfo);
    groupId.value = groupInfo.id || groupInfo.groupId;
    Object.assign(group, groupInfo);
  } else if (options.groupId) {//扫一扫过来的
    groupId.value = options.groupId;
    inviteUserId.value = options.inviteUserId;

    fetchGroupInfoByQRCode();
  }

  // 设置模式
  scanMode.value = options.scanMode === 'true';
  showQRCode.value = !scanMode.value;
});

// 通过二维码查询群组信息
const fetchGroupInfoByQRCode = async () => {
  try {
    const data = {
      groupId: groupId.value, // 不使用 parseInt，保持原始格式
      inviteUserId: inviteUserId.value || 0
    };

    const res = await reqQRCodeGroupFind(data);

    if (res) {
      Object.assign(group, res);
      userInGroup.value = res.status; //状态 0在群里 1 申请中 2 可以申请加群
    }
  } catch (error) {
    userInGroup.value = 999; //状态 999 未知错误
    console.error('查询群组信息失败', error);
    uni.showToast({
      title: error.message || '查询群组信息失败',
      icon: 'none'
    });

    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
};

// 发送消息
const sendMessage = () => {
  uni.navigateTo({
    url: `/pages/group/group-info?id=${groupId.value}`
  });
};

// 保存二维码
const saveQrCode = async () => {
  try {
    uni.showLoading({
      title: '正在保存...'
    });

    const ctx = uni.createCanvasContext('shareCanvas');
    const scale = 2; // 提高清晰度
    const width = 300 * scale;
    const height = 400 * scale;

    // 设置画布大小
    ctx.width = width;
    ctx.height = height;

    // 绘制白色背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, width, height);

    // 绘制头像
    const avatarSize = 60 * scale;
    const avatarX = (width - avatarSize) / 2;
    const avatarY = 30 * scale;

    if (processedHeadImage.value) {
      // 绘制圆形头像
      ctx.save();
      ctx.beginPath();
      ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, 2 * Math.PI);
      ctx.clip();
      ctx.drawImage(processedHeadImage.value, avatarX, avatarY, avatarSize, avatarSize);
      ctx.restore();
    } else {
      // 如果没有头像，绘制默认头像
      // 先绘制圆形裁剪区域
      ctx.save();
      ctx.beginPath();
      ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, 2 * Math.PI);
      ctx.clip();

      // 绘制默认头像
      drawDefaultAvatar(ctx, avatarX, avatarY, avatarSize, scale);

      ctx.restore();
    }

    // 绘制群名
    ctx.setFontSize(16 * scale);
    ctx.setFillStyle('#333333');
    ctx.setTextAlign('center');
    ctx.fillText('群聊：' + (group.name || ''), width / 2, avatarY + avatarSize + 20 * scale);

    // 获取二维码图片
    const qrCodeImage = await new Promise((resolve, reject) => {
      uqrcodeRef.value.toTempFilePath({
        success: (res) => {
          resolve(res.tempFilePath);
        },
        fail: (error) => {
          console.error('获取二维码图片失败:', error);
          reject(error);
        }
      });
    });

    // 绘制二维码（带圆角）
    const qrSize = 200 * scale;
    const qrX = (width - qrSize) / 2;
    const qrY = avatarY + avatarSize + 40 * scale;
    const radius = 5 * scale; // 5px的圆角，需要乘以scale保持比例

    // 保存当前状态
    ctx.save();
    // 创建圆角路径
    ctx.beginPath();
    ctx.moveTo(qrX + radius, qrY);
    ctx.lineTo(qrX + qrSize - radius, qrY);
    ctx.arcTo(qrX + qrSize, qrY, qrX + qrSize, qrY + radius, radius);
    ctx.lineTo(qrX + qrSize, qrY + qrSize - radius);
    ctx.arcTo(qrX + qrSize, qrY + qrSize, qrX + qrSize - radius, qrY + qrSize, radius);
    ctx.lineTo(qrX + radius, qrY + qrSize);
    ctx.arcTo(qrX, qrY + qrSize, qrX, qrY + qrSize - radius, radius);
    ctx.lineTo(qrX, qrY + radius);
    ctx.arcTo(qrX, qrY, qrX + radius, qrY, radius);
    ctx.closePath();
    // 裁剪路径
    ctx.clip();
    // 绘制二维码图片
    ctx.drawImage(qrCodeImage, qrX, qrY, qrSize, qrSize);
    // 恢复之前的状态
    ctx.restore();

    // 绘制底部文字
    const textY = qrY + qrSize + 20 * scale;
    ctx.setFontSize(14 * scale);
    ctx.setFillStyle('#606060');
    ctx.setTextAlign('center');
    ctx.fillText('扫一扫上面的二维码图案，进入群聊', width / 2, textY);

    // 绘制到画布并等待完成
    await new Promise((resolve) => {
      ctx.draw(false, () => {
        setTimeout(resolve, 500);
      });
    });

    // 将画布内容转换为图片
    const res = await new Promise((resolve, reject) => {
      uni.canvasToTempFilePath({
        canvasId: 'shareCanvas',
        fileType: 'png',
        quality: 1,
        width: width,
        height: height,
        destWidth: width,
        destHeight: height,
        success: resolve,
        fail: reject
      });
    });

    // #ifdef H5
    // H5环境下使用a标签下载
    try {
      const a = document.createElement('a');
      a.href = res.tempFilePath;
      a.download = `group_qrcode_${groupId.value}_${new Date().getTime()}.png`;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();

      // 给浏览器一点时间来处理下载
      setTimeout(() => {
        document.body.removeChild(a);
        uni.hideLoading();
        uni.showToast({
          title: '图片已准备好，请在下载栏查看',
          icon: 'none',
          duration: 2000
        });
      }, 100);
    } catch (error) {
      console.error('H5下载失败:', error);
      uni.hideLoading();
      uni.showToast({
        title: '下载失败，请重试',
        icon: 'none'
      });
    }
    // #endif

    // #ifdef APP-PLUS
    // APP环境下保存到相册
    try {
      uni.saveImageToPhotosAlbum({
        filePath: res.tempFilePath,
        success: () => {
          uni.hideLoading();
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
        },
        fail: (error) => {
          console.error('保存到相册失败:', error);
          uni.hideLoading();
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('保存到相册失败:', error);
      uni.hideLoading();
      uni.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
    // #endif

    // #ifndef H5 || APP-PLUS
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
    // #endif
  } catch (error) {
    console.error('保存失败:', error);
    uni.hideLoading();
    uni.showToast({
      title: '保存失败',
      icon: 'none'
    });
  }
};

// 绘制默认头像
const drawDefaultAvatar = (ctx, x, y, size, scale) => {
  const colors = [
    "#5daa31",
    "#c7515a",
    "#e03697",
    "#85029b",
    "#c9b455",
    "#326eb6",
  ];

  // 计算颜色索引
  let hash = 0;
  const name = group.name || 'Group';
  for (let i = 0; i < name.length; i++) {
    hash += name.charCodeAt(i);
  }
  const colorIndex = hash % colors.length;

  // 绘制背景
  ctx.setFillStyle(colors[colorIndex]);
  ctx.fillRect(x, y, size, size);

  // 绘制文字
  ctx.setFillStyle('#ffffff');
  ctx.setFontSize(size * 0.5);
  ctx.setTextAlign('center');
  ctx.setTextBaseline('middle');
  const firstChar = (group.name || 'G').substr(0, 1).toUpperCase();
  ctx.fillText(firstChar, x + size / 2, y + size / 2);
};

// 加入群聊
const joinGroup = async () => {
  try {
    const data = {
      groupId: groupId.value, // 不使用 parseInt，保持原始格式
      inviteUserId: inviteUserId.value || 0
    };

    uni.showLoading({
      title: '正在加入...'
    });

    //状态 0 加入失败 1 加入成功 2 等待审核
    const res = await reqQRCodeGroupAdd(data);
    uni.hideLoading();
    console.log('加入群聊结果:', res);
    if (res.status === 0) {
      uni.showToast({
        title: '加入失败',
        icon: 'none'
      });
      return;
    } else if (res.status === 2) {
      uni.showToast({
        title: '等待审核',
        icon: 'none'
      });
      userInGroup.value = 1;
      return;
    }

    uni.showToast({
      title: '加入成功',
      icon: 'success'
    });

    setTimeout(() => {
      uni.navigateTo({
        url: `/pages/group/group-info?id=${groupId.value}`
      });
    }, 1500);
  } catch (err) {
    console.error('加入群聊失败', err);
    uni.showToast({
      title: err.message || '加入群聊失败',
      icon: 'none'
    });
  }
};
</script>

<style lang="scss" scoped>
.im-nav-bar.s-has-bottom-border {
  border: none;
}

.group-qrcode {
  background: #fff;

  .body_box {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 50px 30px;

    .box_box {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      padding: 30px;
      border-radius: 12px;
      background-color: #fff;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

      .bottom_text {
        font-size: 14px;
        color: #606060;
        margin-top: 15px;
      }

      .qr_img_box {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 10px;
        padding: 15px;
        background-color: #fff;
        border-radius: 8px;

        :deep(.uqrcode) {
          border-radius: 5px;
          overflow: hidden;
        }

        .qrcode-container {
          width: 200px;
          height: 200px;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .color-picker {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        gap: 15px;

        .color-option {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s;
          border: 2px solid transparent;

          &.active {
            transform: scale(1.2);
            border-color: #1296db;
          }
        }
      }

      .img_box {
        width: 100%;

        .avatar-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          border-radius: 5px;
          background-color: #fff;

          .img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
          }

          .username {
            font-weight: 500;
            color: #333;
            font-size: 16px;
            text-align: center;
          }
        }
      }
    }
  }

  .img_box_noshowQRCode {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    color: black;
    padding-top: 25rpx;

    .username {
      margin-top: 25rpx;
    }
  }

  .action_buttons {
    position: absolute;
    bottom: 20rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 50rpx 0;

    .button_row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80%;
      height: 75rpx;
      color: white;
      background-color: #279BFD;
      border-radius: 10rpx;
    }

    .action_button {
      height: 90rpx;
      line-height: 90rpx;
      border-radius: 45rpx;
      font-size: 32rpx;
      color: #fff;
      text-align: center;
    }

    .join_button {
      background-color: #1296db;
    }

    .apply_button {
      background-color: #4CAF50;
    }

    .message_button {
      background-color: #1296db;
    }

    .cancel_button {
      background-color: #9e9e9e;
    }
  }

  .share-canvas {
    position: fixed;
    width: 750px;
    height: 1000px;
    top: -9999px;
    left: -9999px;
  }
}
</style>
